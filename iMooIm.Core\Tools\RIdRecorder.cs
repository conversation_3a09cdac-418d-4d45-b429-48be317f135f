﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Tools.RIdRecorder
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

#nullable disable
namespace iMooIm.Core.Tools;

internal static class RIdRecorder
{
  private static int _rid = 1005;

  internal static int RId
  {
    get
    {
      ++RIdRecorder._rid;
      return RIdRecorder._rid - 1;
    }
    set => RIdRecorder._rid = value;
  }
}
