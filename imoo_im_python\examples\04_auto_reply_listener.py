#!/usr/bin/env python3
"""
自动回复消息监听示例

这个示例展示了如何持续监听消息并自动回复。
当收到内容为"test"的消息时，会自动回复"收到"给对应的好友。
"""

import logging
import sys
import time
import signal
import threading
import json
import uuid
from datetime import datetime
from imoo_im_core.im_client import ImClient
from imoo_im_core.data.account.device_info import DeviceInfo
from imoo_im_core.data.account.app_info import AppInfo
from imoo_im_core.events.log_event import LogEvent
from imoo_im_core.events.text_message_event import TextMessageEvent
from imoo_im_core.events.save_sync_key_event import SaveSyncKeyEvent
from imoo_im_core.data.enums.log_level import LogLevel
from imoo_im_core.data.messages.single_message_request_message import SingleMessageRequestMessage
from imoo_im_core.tools.rid_recorder import RIdRecorder


class AutoReplyListener:
    """自动回复消息监听器类"""
    
    def __init__(self):
        self.logger = logging.getLogger("AutoReplyListener")
        self.running = False
        self.client = None
        self.message_count = 0
        self.reply_count = 0
        
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                # 可以添加文件日志处理器
                # logging.FileHandler('auto_reply_listener.log', encoding='utf-8')
            ]
        )
        
    def log_level_to_python(self, level: LogLevel) -> int:
        """将 iMooIm 日志级别转换为 Python logging 级别"""
        mapping = {
            LogLevel.TRACE: logging.DEBUG,
            LogLevel.DEBUG: logging.DEBUG,
            LogLevel.INFORMATION: logging.INFO,
            LogLevel.WARNING: logging.WARNING,
            LogLevel.ERROR: logging.ERROR,
            LogLevel.CRITICAL: logging.CRITICAL,
            LogLevel.NONE: logging.NOTSET
        }
        return mapping.get(level, logging.INFO)
        
    def create_device_info(self) -> DeviceInfo:
        """创建设备信息 - 使用真实设备信息"""
        device_info = DeviceInfo()
        
        # 使用真实设备信息
        device_info.baseband_version = "I20-V2.3.0-********-15.12.02"
        device_info.build_number = "OPM1.171019.026 release-keys"
        device_info.bind_number = "anconbjwfhaoanth"
        device_info.chip_id = "000000700000010022a916c0000c0007"
        device_info.account_id = "bd30ea5b78704ce48cf55d6e0494131454589507"
        device_info.model_number = "Z6_DFB"
        device_info.sdk_version = 27
        device_info.sys_name = "Z6_DFB 8.1.0"
        device_info.sys_version = "8.1.0"
        device_info.eebbk_key = ""
        
        return device_info
        
    def on_log(self, sender, event: LogEvent):
        """处理日志事件"""
        core_logger = logging.getLogger("iMooIm-Core")
        python_level = self.log_level_to_python(event.level)
        core_logger.log(python_level, event.message)
        
    def send_reply_message(self, friend_id: int, content: str) -> bool:
        """
        发送回复消息
        
        Args:
            friend_id: 好友ID
            content: 回复内容
            
        Returns:
            bool: 发送是否成功
        """
        try:
            if not self.client or not self.client._login_state:
                self.logger.error("客户端未登录，无法发送回复")
                return False
                
            # 创建消息请求
            message_request = SingleMessageRequestMessage()
            message_request.r_id = RIdRecorder.get_r_id()
            message_request.receiver_id = friend_id
            message_request.account_id = self.client._im_account_id
            message_request.regist_id = self.client._regist_id
            message_request.msg_type = 1  # 文本消息类型
            message_request.content_type = 3  # 内容类型
            message_request.need_response = 0
            message_request.no_sensitivity = 0
            message_request.msg_id = str(uuid.uuid4()).replace('-', '')

            # 构造消息内容
            msg_content = {"content": content}
            message_request.msg = json.dumps(msg_content).encode('utf-8')

            # 发送消息
            if self.client._tcp_connection:
                self.client._tcp_connection.send(message_request.encode())
                self.logger.info(f"✅ 成功发送回复给好友 {friend_id}: {content}")
                return True
            else:
                self.logger.error("TCP连接为空，无法发送回复")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 发送回复失败: {e}")
            return False
        
    def on_text_message(self, sender, event: TextMessageEvent):
        """处理接收到的文本消息"""
        self.message_count += 1
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        self.logger.info("=" * 60)
        self.logger.info(f"📨 收到第 {self.message_count} 条消息")
        self.logger.info(f"⏰ 时间: {timestamp}")
        self.logger.info(f"💬 内容: {event.content}")
        self.logger.info(f"🆔 对话ID: {event.dialog_id}")
        self.logger.info(f"👤 好友ID: {event.friend_id}")
        
        # 检查是否需要自动回复
        if event.content.strip().lower() == "test":
            self.logger.info("🤖 检测到触发词 'test'，准备自动回复...")
            
            # 发送自动回复
            reply_content = "收到"
            success = self.send_reply_message(event.friend_id, reply_content)
            
            if success:
                self.reply_count += 1
                self.logger.info(f"🎯 自动回复成功！已回复 {self.reply_count} 次")
            else:
                self.logger.error("🚫 自动回复失败")
        else:
            self.logger.info("ℹ️  消息内容不是 'test'，不触发自动回复")
            
        self.logger.info("=" * 60)
        
    def on_save_sync_key(self, sender, event: SaveSyncKeyEvent):
        """处理同步键保存事件"""
        self.logger.info(f"🔑 需要保存同步键: {event.sync_key}")
        # 在实际应用中，应该将同步键保存到持久化存储中
        
    def signal_handler(self, signum, frame):
        """处理中断信号"""
        self.logger.info(f"\n🛑 收到中断信号 {signum}，正在优雅关闭...")
        self.running = False
        
    def start_listening(self):
        """开始监听消息"""
        self.setup_logging()
        
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.logger.info("=" * 70)
        self.logger.info("🤖 iMooIm Python 自动回复客户端")
        self.logger.info("=" * 70)
        self.logger.info("💡 功能说明:")
        self.logger.info("   - 持续监听好友消息")
        self.logger.info("   - 当收到内容为 'test' 的消息时自动回复 '收到'")
        self.logger.info("   - 按 Ctrl+C 可以优雅地停止程序")
        self.logger.info("=" * 70)
        
        # 创建设备和应用信息
        device_info = self.create_device_info()
        app_info = AppInfo()
        
        # 创建客户端
        self.client = ImClient(device_info, app_info, 0)
        
        # 绑定事件处理器
        self.client.on_log = self.on_log
        self.client.on_receive_text_message = self.on_text_message
        self.client.on_save_sync_key_event = self.on_save_sync_key
        
        try:
            # 初始化和登录
            self.logger.info("🔧 正在初始化客户端...")
            self.client.init()
            self.logger.info("✅ 客户端初始化完成")
            
            self.logger.info("🔐 开始登录流程...")
            self.client.login()
            self.logger.info("✅ 登录成功！")
            
            self.logger.info("👂 开始监听消息...")
            self.logger.info("🎯 自动回复已启用 - 发送 'test' 来测试自动回复功能")
            
            self.running = True
            start_time = datetime.now()
            
            # 主监听循环
            while self.running:
                time.sleep(1)
                
                # 每60秒显示一次统计信息
                if int(time.time()) % 60 == 0:
                    elapsed = datetime.now() - start_time
                    self.logger.info(f"📊 运行时间: {elapsed}, 已接收: {self.message_count} 条, 已回复: {self.reply_count} 条")
                    
        except KeyboardInterrupt:
            self.logger.info("\n🛑 收到键盘中断，正在停止...")
            
        except Exception as e:
            self.logger.error(f"❌ 发生错误: {e}")
            import traceback
            self.logger.error("详细错误信息:")
            self.logger.error(traceback.format_exc())
            
        finally:
            # 关闭客户端
            if self.client:
                self.logger.info("🔌 正在关闭客户端...")
                self.client.close()
                self.logger.info("✅ 客户端已关闭")
                
            # 显示最终统计
            self.logger.info("=" * 70)
            self.logger.info(f"📊 监听结束统计:")
            self.logger.info(f"   - 总共接收了 {self.message_count} 条消息")
            self.logger.info(f"   - 总共自动回复了 {self.reply_count} 条消息")
            self.logger.info("=" * 70)


def main():
    """主函数"""
    listener = AutoReplyListener()
    listener.start_listening()


if __name__ == "__main__":
    main()
