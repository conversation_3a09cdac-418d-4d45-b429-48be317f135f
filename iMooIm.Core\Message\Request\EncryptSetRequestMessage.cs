﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Message.Request.EncryptSetRequestMessage
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;
using System;

#nullable enable
namespace iMooIm.Core.Message.Request;

[CommandId(33)]
internal class EncryptSetRequestMessage : BaseMessage
{
  [TagId(11)]
  public byte[] EncryptKey { get; set; } = Array.Empty<byte>();

  [TagId(10)]
  public int EncryptType { get; set; }
}
