﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Message.Request.SingleMessageRequestMessage
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;
using System;

#nullable enable
namespace iMooIm.Core.Message.Request;

[CommandId(40)]
internal class SingleMessageRequestMessage : BaseMessage
{
  [TagId(11)]
  public long AccountId { get; set; }

  [TagId(16 /*0x10*/)]
  public int ContentType { get; set; }

  [TagId(14)]
  public byte[] Message { get; set; } = Array.Empty<byte>();

  [TagId(15)]
  public string MsgId { get; set; } = Guid.NewGuid().ToString("N");

  [TagId(13)]
  public int MsgType { get; set; }

  [TagId(2)]
  public int NeedResponse { get; set; }

  [TagId(18)]
  public int NoSensitivity { get; set; }

  [TagId(10)]
  public long ReceiverId { get; set; }

  [TagId(12)]
  public long RegistId { get; set; }
}
