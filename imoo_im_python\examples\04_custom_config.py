#!/usr/bin/env python3
"""
自定义配置示例

这个示例展示了如何使用配置文件来管理设备信息和应用设置，
使得程序更易于维护和部署。
"""

import logging
import sys
import json
import os
from pathlib import Path
from typing import Dict, Any
from imoo_im_core.im_client import ImClient
from imoo_im_core.data.account.device_info import DeviceInfo
from imoo_im_core.data.account.app_info import AppInfo
from imoo_im_core.events.log_event import LogEvent
from imoo_im_core.events.text_message_event import TextMessageEvent
from imoo_im_core.data.enums.log_level import LogLevel


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = {}
        self.logger = logging.getLogger("ConfigManager")
        
    def create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        return {
            "device_info": {
                "baseband_version": "YOUR_BASEBAND_VERSION",
                "build_number": "YOUR_BUILD_NUMBER", 
                "bind_number": "YOUR_BIND_NUMBER",
                "chip_id": "YOUR_CHIP_ID",
                "account_id": "YOUR_ACCOUNT_ID",
                "model_number": "YOUR_MODEL_NUMBER",
                "sdk_version": 27,
                "sys_name": "YOUR_SYS_NAME",
                "sys_version": "YOUR_SYS_VERSION",
                "eebbk_key": ""
            },
            "app_info": {
                "app_key": "YOUR_APP_KEY"  # 可选，留空使用默认值
            },
            "connection": {
                "server_host": "gw.im.okii.com",  # 默认服务器地址
                "server_port": 8000,              # 默认端口
                "timeout": 30                     # 连接超时时间（秒）
            },
            "logging": {
                "level": "INFO",                  # 日志级别
                "file_logging": False,            # 是否启用文件日志
                "log_file": "imoo_client.log"     # 日志文件名
            },
            "features": {
                "auto_reconnect": True,           # 自动重连
                "message_history": True,          # 保存消息历史
                "sync_key_storage": True          # 同步键存储
            }
        }
        
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_file):
                self.logger.info(f"配置文件 {self.config_file} 不存在，创建默认配置")
                self.config = self.create_default_config()
                self.save_config()
                return False
                
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
                
            self.logger.info(f"成功加载配置文件: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self.config = self.create_default_config()
            return False
            
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"配置文件已保存: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
            
    def get_device_info(self) -> DeviceInfo:
        """从配置创建设备信息"""
        device_config = self.config.get("device_info", {})
        
        device_info = DeviceInfo()
        device_info.baseband_version = device_config.get("baseband_version", "")
        device_info.build_number = device_config.get("build_number", "")
        device_info.bind_number = device_config.get("bind_number", "")
        device_info.chip_id = device_config.get("chip_id", "")
        device_info.account_id = device_config.get("account_id", "")
        device_info.model_number = device_config.get("model_number", "")
        device_info.sdk_version = device_config.get("sdk_version", 27)
        device_info.sys_name = device_config.get("sys_name", "")
        device_info.sys_version = device_config.get("sys_version", "")
        device_info.eebbk_key = device_config.get("eebbk_key", "")
        
        return device_info
        
    def get_app_info(self) -> AppInfo:
        """从配置创建应用信息"""
        app_config = self.config.get("app_info", {})
        
        app_info = AppInfo()
        if app_config.get("app_key"):
            app_info.app_key = app_config["app_key"]
            
        return app_info
        
    def get_connection_config(self) -> Dict[str, Any]:
        """获取连接配置"""
        return self.config.get("connection", {})
        
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.config.get("logging", {})
        
    def get_features_config(self) -> Dict[str, Any]:
        """获取功能配置"""
        return self.config.get("features", {})
        
    def validate_config(self) -> bool:
        """验证配置的完整性"""
        device_info = self.config.get("device_info", {})
        required_fields = [
            "baseband_version", "build_number", "bind_number", 
            "chip_id", "account_id", "model_number", "sys_name", "sys_version"
        ]
        
        missing_fields = []
        for field in required_fields:
            value = device_info.get(field, "")
            if not value or value.startswith("YOUR_"):
                missing_fields.append(field)
                
        if missing_fields:
            self.logger.warning(f"配置中以下字段需要设置: {missing_fields}")
            return False
            
        return True


def setup_logging(config_manager: ConfigManager):
    """根据配置设置日志系统"""
    log_config = config_manager.get_logging_config()
    
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }
    
    level = level_map.get(log_config.get("level", "INFO"), logging.INFO)
    
    handlers = [logging.StreamHandler(sys.stdout)]
    
    # 如果启用文件日志
    if log_config.get("file_logging", False):
        log_file = log_config.get("log_file", "imoo_client.log")
        handlers.append(logging.FileHandler(log_file, encoding='utf-8'))
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers
    )
    
    return logging.getLogger("CustomConfig")


def main():
    """主函数"""
    # 初始化配置管理器
    config_manager = ConfigManager("config.json")
    
    # 加载配置
    config_loaded = config_manager.load_config()
    
    # 设置日志
    logger = setup_logging(config_manager)
    
    logger.info("=" * 60)
    logger.info("iMooIm Python 客户端 - 自定义配置示例")
    logger.info("=" * 60)
    
    if not config_loaded:
        logger.warning("⚠️  首次运行，已创建默认配置文件 config.json")
        logger.warning("⚠️  请编辑配置文件并填入您的设备信息后重新运行")
        return
        
    # 验证配置
    if not config_manager.validate_config():
        logger.error("❌ 配置验证失败，请检查并完善配置文件")
        logger.info("💡 请确保所有以 'YOUR_' 开头的字段都已正确填写")
        return
        
    logger.info("✅ 配置验证通过")
    
    # 从配置创建设备和应用信息
    device_info = config_manager.get_device_info()
    app_info = config_manager.get_app_info()
    connection_config = config_manager.get_connection_config()
    features_config = config_manager.get_features_config()
    
    # 创建客户端
    client = ImClient(device_info, app_info, 0)
    
    # 设置事件回调
    def on_log(sender, event: LogEvent):
        core_logger = logging.getLogger("iMooIm-Core")
        level_map = {
            LogLevel.DEBUG: logging.DEBUG,
            LogLevel.INFORMATION: logging.INFO,
            LogLevel.WARNING: logging.WARNING,
            LogLevel.ERROR: logging.ERROR,
            LogLevel.CRITICAL: logging.CRITICAL
        }
        level = level_map.get(event.level, logging.INFO)
        core_logger.log(level, event.message)
        
    def on_text_message(sender, event: TextMessageEvent):
        logger.info(f"📨 收到消息: {event.content}")
        
        # 如果启用消息历史功能
        if features_config.get("message_history", False):
            # 这里可以添加消息存储逻辑
            logger.debug("💾 消息已记录到历史")
    
    client.on_log = on_log
    client.on_receive_text_message = on_text_message
    
    try:
        # 使用配置中的连接参数
        server_host = connection_config.get("server_host", "gw.im.okii.com")
        server_port = connection_config.get("server_port", 8000)
        
        logger.info(f"🔧 正在连接到 {server_host}:{server_port}")
        client.init()
        logger.info("✅ 客户端初始化完成")
        
        logger.info("🔐 开始登录流程...")
        client.login()
        logger.info("✅ 登录成功！")
        
        # 显示当前配置信息
        logger.info("📋 当前配置:")
        logger.info(f"   设备型号: {device_info.model_number}")
        logger.info(f"   系统版本: {device_info.sys_version}")
        logger.info(f"   自动重连: {features_config.get('auto_reconnect', False)}")
        logger.info(f"   消息历史: {features_config.get('message_history', False)}")
        
        # 保持连接
        import time
        logger.info("⏰ 保持连接30秒...")
        time.sleep(30)
        
    except Exception as e:
        logger.error(f"❌ 发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
        
    finally:
        logger.info("🔌 正在关闭客户端...")
        client.close()
        logger.info("✅ 客户端已关闭")


if __name__ == "__main__":
    main()
