﻿using System.Text;
using Greensoft.TlvLib;
using iMooIm.Core.Data.Tlv;
using iMooIm.Core.Tools;

namespace iMooIm.Core.Data;

public class Entity
{
    public Dictionary<int,object> Data { get; set; } = new();
    public int CommandId { get; set; }
    public byte[] Encode()
    {
        using var mainMs = new MemoryStream();
        using var subMs = new MemoryStream();
        foreach (var tagValue in Data)
        {
            byte[] data = [];
            if (tagValue.Value is int)
            {
                data = (tagValue.Value as int? ?? 0).Encode2Bytes();
            }
            if (tagValue.Value is long)
            {
                data = (tagValue.Value as long? ?? 0).Encode2Bytes();
            }
            if (tagValue.Value is byte[] bytes)
            {
                data = bytes;
            }
            if (tagValue.Value is string value)
            {
                data = Encoding.UTF8.GetBytes(value);
            }
            subMs.Write(TlvEncoder.EncodeTlv(tagValue.Key,data.Length,data));
        }
        mainMs.Write(TlvEncoder.EncodeNestedTlv(32,CommandId,subMs.ToArray().Length,subMs.ToArray()));
        return mainMs.ToArray();
    }
}