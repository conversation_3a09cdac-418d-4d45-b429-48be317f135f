﻿namespace iMooIm.Core.Data.Tlv;

public static class TlvEncoder
{
    public static byte[] EncodeTlv(int tag, int length, byte[] value)
    {
        byte[] tagBytes = EncodeTag(0,0,tag);
        byte[] lengthBytes = EncodeLength(length);
        byte[] tlvBytes = new byte[tagBytes.Length + lengthBytes.Length + value.Length];
        Buffer.BlockCopy(tagBytes, 0, tlvBytes, 0, tagBytes.Length);
        Buffer.BlockCopy(lengthBytes, 0, tlvBytes, tagBytes.Length, lengthBytes.Length);
        Buffer.BlockCopy(value, 0, tlvBytes, tagBytes.Length + lengthBytes.Length, value.Length);
        return tlvBytes;
    }
    public static byte[] EncodeNestedTlv(int dataType,int tag, int length, byte[] value)
    {
        byte[] tagBytes = EncodeTag(dataType,0,tag);
        byte[] lengthBytes = EncodeLength(length);
        byte[] tlvBytes = new byte[tagBytes.Length + lengthBytes.Length + value.Length];
        Buffer.BlockCopy(tagBytes, 0, tlvBytes, 0, tagBytes.Length);
        Buffer.BlockCopy(lengthBytes, 0, tlvBytes, tagBytes.Length, lengthBytes.Length);
        Buffer.BlockCopy(value, 0, tlvBytes, tagBytes.Length + lengthBytes.Length, value.Length);
        return tlvBytes;
    }

    private static byte[] EncodeLength(int i) {
        if (i < 0) {
            throw new ArgumentException("the length must not less than 0.");
        }
        if (i < 128) {
            return new byte[]{(byte) (i & 127)};
        }
        int calcResult = (int) Math.Ceiling(Math.Log(i + 1.0d) / Math.Log(128.0d));
        return LengthEncode(EncodeVariableLengthInteger(0, calcResult, i), calcResult);

        static byte[] LengthEncode(int i, int i2) {
            byte[] bArr = new byte[i2];
            int length = bArr.Length;
            bArr[0] = (byte) ((i >> ((i2 - 1) * 8)) & 255);
            for (int i3 = 1; i3 < length; i3++) {
                bArr[i3] = (byte) ((i >> (((i2 - i3) - 1) * 8)) & 255);
            }
            return bArr;
        }
    }
    private static int EncodeVariableLengthInteger(int i, int i2, int i3) {
        int i4 = 0;
        while (true) {
            int i5 = i2 - 1;
            if (i4 >= i5) {
                return i | ((i3 >> (i5 * 7)) & 127);
            }
            i |= (((i3 >> (i4 * 7)) & 127) | 128) << ((i5 - i4) * 8);
            i4++;
        }
    }
    private static byte[] EncodeTag(int i, int i2, int i3) {
        int i4;
        int i5;
        int i6 = i | i2;
        int i7 = i6 | i3;
        if (i3 >= 31) {
            i5= (int) Math.Ceiling(Math.Log(i3 + 1.0d) / Math.Log(128.0d));
            i4 = EncodeVariableLengthInteger((i6 | 128) << (i5 * 8), i5, i3);
        } else {
            i4 = i7;
            i5 = 0;
        }
        return IntToBytes(i4, i5);
    }
    private static byte[] IntToBytes(int i, int i2) {
        byte[] bArr = new byte[i2 + 1];
        int length = bArr.Length;
        bArr[0] = (byte) ((i >> (i2 * 8)) & 255);
        for (int i3 = 0; i3 < length; i3++) {
            bArr[i3] = (byte) ((i >> ((i2 - i3) * 8)) & 255);
        }
        return bArr;
    }
}