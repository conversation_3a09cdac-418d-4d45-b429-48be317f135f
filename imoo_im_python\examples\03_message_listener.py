#!/usr/bin/env python3
"""
消息监听示例

这个示例展示了如何持续监听和处理接收到的消息。
适用于需要长时间运行的聊天机器人或消息处理程序。
"""

import logging
import sys
import time
import signal
import threading
from datetime import datetime
from imoo_im_core.im_client import ImClient
from imoo_im_core.data.account.device_info import DeviceInfo
from imoo_im_core.data.account.app_info import AppInfo
from imoo_im_core.events.log_event import LogEvent
from imoo_im_core.events.text_message_event import TextMessageEvent
from imoo_im_core.events.save_sync_key_event import SaveSyncKeyEvent
from imoo_im_core.data.enums.log_level import LogLevel


class MessageListener:
    """消息监听器类"""
    
    def __init__(self):
        self.logger = logging.getLogger("MessageListener")
        self.running = False
        self.client = None
        self.message_count = 0
        
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                # 可以添加文件日志处理器
                # logging.FileHandler('message_listener.log', encoding='utf-8')
            ]
        )
        
    def log_level_to_python(self, level: LogLevel) -> int:
        """将 iMooIm 日志级别转换为 Python logging 级别"""
        mapping = {
            LogLevel.TRACE: logging.DEBUG,
            LogLevel.DEBUG: logging.DEBUG,
            LogLevel.INFORMATION: logging.INFO,
            LogLevel.WARNING: logging.WARNING,
            LogLevel.ERROR: logging.ERROR,
            LogLevel.CRITICAL: logging.CRITICAL,
            LogLevel.NONE: logging.NOTSET
        }
        return mapping.get(level, logging.INFO)
        
    def create_device_info(self) -> DeviceInfo:
        """创建设备信息 - 请修改为您的实际设备信息"""
        device_info = DeviceInfo()
        
        # 请修改为您的实际设备信息
        device_info.baseband_version = "YOUR_BASEBAND_VERSION"
        device_info.build_number = "YOUR_BUILD_NUMBER"
        device_info.bind_number = "YOUR_BIND_NUMBER"
        device_info.chip_id = "YOUR_CHIP_ID"
        device_info.account_id = "YOUR_ACCOUNT_ID"
        device_info.model_number = "YOUR_MODEL_NUMBER"
        device_info.sdk_version = 27
        device_info.sys_name = "YOUR_SYS_NAME"
        device_info.sys_version = "YOUR_SYS_VERSION"
        device_info.eebbk_key = ""
        
        return device_info
        
    def on_log(self, sender, event: LogEvent):
        """处理日志事件"""
        core_logger = logging.getLogger("iMooIm-Core")
        python_level = self.log_level_to_python(event.level)
        core_logger.log(python_level, event.message)
        
    def on_text_message(self, sender, event: TextMessageEvent):
        """处理接收到的文本消息"""
        self.message_count += 1
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        self.logger.info("=" * 50)
        self.logger.info(f"📨 收到第 {self.message_count} 条消息")
        self.logger.info(f"⏰ 时间: {timestamp}")
        self.logger.info(f"💬 内容: {event.content}")
        self.logger.info(f"🆔 对话ID: {event.dialog_id}")
        self.logger.info(f"👤 好友ID: {event.friend_id}")
        self.logger.info("=" * 50)
        
        # 在这里可以添加自动回复逻辑
        self.process_message(event)
        
    def process_message(self, event: TextMessageEvent):
        """
        处理消息的业务逻辑
        
        您可以在这里添加：
        - 自动回复逻辑
        - 消息存储到数据库
        - 触发其他业务流程
        - 消息转发等
        """
        content = event.content.lower().strip()
        
        # 示例：简单的关键词响应
        if "hello" in content or "你好" in content:
            self.logger.info("🤖 检测到问候消息，可以在这里添加自动回复逻辑")
            
        elif "help" in content or "帮助" in content:
            self.logger.info("🤖 检测到帮助请求，可以在这里添加帮助信息回复")
            
        elif "time" in content or "时间" in content:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.logger.info(f"🤖 检测到时间查询，当前时间: {current_time}")
            
        # 可以添加更多的消息处理逻辑
        
    def on_save_sync_key(self, sender, event: SaveSyncKeyEvent):
        """处理同步键保存事件"""
        self.logger.info(f"🔑 需要保存同步键: {event.sync_key}")
        # 在实际应用中，应该将同步键保存到持久化存储中
        
    def signal_handler(self, signum, frame):
        """处理中断信号"""
        self.logger.info(f"\n🛑 收到中断信号 {signum}，正在优雅关闭...")
        self.running = False
        
    def start_listening(self):
        """开始监听消息"""
        self.setup_logging()
        
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.logger.info("=" * 60)
        self.logger.info("iMooIm Python 客户端 - 消息监听示例")
        self.logger.info("=" * 60)
        self.logger.info("💡 按 Ctrl+C 可以优雅地停止程序")
        
        # 创建设备和应用信息
        device_info = self.create_device_info()
        app_info = AppInfo()
        
        # 创建客户端
        self.client = ImClient(device_info, app_info, 0)
        
        # 绑定事件处理器
        self.client.on_log = self.on_log
        self.client.on_receive_text_message = self.on_text_message
        self.client.on_save_sync_key_event = self.on_save_sync_key
        
        try:
            # 初始化和登录
            self.logger.info("🔧 正在初始化客户端...")
            self.client.init()
            self.logger.info("✅ 客户端初始化完成")
            
            self.logger.info("🔐 开始登录流程...")
            self.client.login()
            self.logger.info("✅ 登录成功！")
            
            self.logger.info("👂 开始监听消息...")
            self.logger.info("📊 统计信息将实时显示")
            
            self.running = True
            start_time = datetime.now()
            
            # 主监听循环
            while self.running:
                time.sleep(1)
                
                # 每60秒显示一次统计信息
                if int(time.time()) % 60 == 0:
                    elapsed = datetime.now() - start_time
                    self.logger.info(f"📊 运行时间: {elapsed}, 已接收消息: {self.message_count} 条")
                    
        except KeyboardInterrupt:
            self.logger.info("\n🛑 收到键盘中断，正在停止...")
            
        except Exception as e:
            self.logger.error(f"❌ 发生错误: {e}")
            import traceback
            self.logger.error("详细错误信息:")
            self.logger.error(traceback.format_exc())
            
        finally:
            # 关闭客户端
            if self.client:
                self.logger.info("🔌 正在关闭客户端...")
                self.client.close()
                self.logger.info("✅ 客户端已关闭")
                
            # 显示最终统计
            self.logger.info("=" * 60)
            self.logger.info(f"📊 监听结束，总共接收了 {self.message_count} 条消息")
            self.logger.info("=" * 60)


def main():
    """主函数"""
    listener = MessageListener()
    listener.start_listening()


if __name__ == "__main__":
    main()
