﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Message.Request.LoginRequestMessage
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;

#nullable enable
namespace iMooIm.Core.Message.Request;

[CommandId(3)]
internal class LoginRequestMessage : BaseMessage
{
  [TagId(16 /*0x10*/)]
  public string ApnsToken { get; set; } = "";

  [TagId(15)]
  public long ApnsType { get; set; }

  [TagId(24)]
  public string BaseRequestParam { get; set; } = "";

  [TagId(17)]
  public string DeviceToken { get; set; } = "";

  [TagId(25)]
  public string EebbkKey { get; set; } = "";

  [TagId(18)]
  public string ImSdkVersionName { get; set; } = "";

  [TagId(20)]
  public int Platform { get; set; }

  [TagId(19)]
  public int PushType { get; set; }

  [TagId(10)]
  public long RegistId { get; set; }

  [TagId(11)]
  public string RegistToken { get; set; } = "";

  [TagId(12)]
  public long SdkVersion { get; set; }

  [TagId(21)]
  public long Timestamp { get; set; }
}
