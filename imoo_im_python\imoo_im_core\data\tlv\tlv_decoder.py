"""
TLV 解码器 - 解码 TLV 格式的数据
"""

from typing import List, Optional
from .tlv_decode_result import TlvDecodeResult
from .tlv_byte_buffer import TlvByteBuffer


class TlvDecoder:
    """TLV 解码器"""
    
    _debug_enabled = False
    
    @staticmethod
    def decode(data: bytes) -> TlvDecodeResult:
        """
        解码 TLV 数据
        
        Args:
            data: 要解码的字节数组
            
        Returns:
            解码结果
        """
        return TlvDecoder._decode_tlv(data)
    
    @staticmethod
    def _decode_tlv(data: bytes) -> TlvDecodeResult:
        """
        解码单个 TLV 数据
        
        Args:
            data: 要解码的字节数组
            
        Returns:
            解码结果
        """
        if len(data) == 0:
            return TlvDecodeResult()
        
        # 解析标签
        tag_length = TlvDecoder.get_tag_length(data)
        tag_bytes = data[:tag_length]
        
        # 解析长度字段
        length_field_length = TlvDecoder.get_length_field_length(data, tag_length)
        length_bytes = data[tag_length:tag_length + length_field_length]
        value_length = TlvDecoder.get_value_length(length_bytes)
        
        # 解析值
        value_bytes = data[tag_length + length_field_length:tag_length + length_field_length + value_length]
        
        value = value_bytes
        if TlvDecoder.get_data_type(tag_bytes) == 32:
            value = TlvDecoder.decode_nested_tlv(value_bytes)
        
        # 构造结果
        result = TlvDecodeResult()
        result.frame_type = TlvDecoder.get_frame_type(tag_bytes)
        result.data_type = TlvDecoder.get_data_type(tag_bytes)
        result.tag_value = TlvDecoder.get_tag_value(tag_bytes)
        result.length = value_length
        result.value = value
        
        return result
    
    @staticmethod
    def decode_nested_tlv(data: bytes) -> Optional[List[TlvDecodeResult]]:
        """
        解码嵌套 TLV 数据
        
        Args:
            data: 要解码的字节数组
            
        Returns:
            解码结果列表
        """
        if not data or len(data) == 0:
            return None
        
        buffer = TlvByteBuffer(data)
        results = []
        
        while buffer.has_next_tlv_data():
            next_data = buffer.cut_next_tlv_data()
            if next_data:
                results.append(TlvDecoder._decode_tlv(next_data))
        
        return results
    
    @staticmethod
    def get_tag_length(data: bytes) -> int:
        """
        获取标签长度
        
        Args:
            data: 数据字节数组
            
        Returns:
            标签长度
        """
        if len(data) == 0:
            return 0
        
        first_byte = data[0]
        if (first_byte & 0x80) != 0x80:
            return 1
        else:
            # 多字节标签
            length = 1
            for i in range(1, len(data)):
                length += 1
                if (data[i] & 0x80) != 0x80:
                    break
            return length
    
    @staticmethod
    def get_length_field_length(data: bytes, tag_length: int) -> int:
        """
        获取长度字段长度

        Args:
            data: 数据字节数组
            tag_length: 标签长度

        Returns:
            长度字段长度
        """
        if len(data) <= tag_length:
            return 0

        # 按照 C# 版本的逻辑：计算连续的长度字节数
        count = 0
        offset = tag_length
        while offset < len(data):
            count += 1
            if (data[offset] & 0x80) == 0:
                return count
            offset += 1
        return 0
    
    @staticmethod
    def get_frame_type(data: bytes) -> int:
        """
        获取帧类型
        
        Args:
            data: 标签字节数组
            
        Returns:
            帧类型
        """
        if len(data) == 0:
            return 0
        return (data[0] & 0xC0) >> 6
    
    @staticmethod
    def get_data_type(data: bytes) -> int:
        """
        获取数据类型
        
        Args:
            data: 标签字节数组
            
        Returns:
            数据类型
        """
        if len(data) == 0:
            return 0
        return data[0] & 32
    
    @staticmethod
    def get_tag_value(data: bytes) -> int:
        """
        获取标签值
        
        Args:
            data: 标签字节数组
            
        Returns:
            标签值
        """
        if len(data) == 0:
            return 0
        
        if (data[0] & 0x80) != 0x80:
            return data[0] & 0x1F
        else:
            return TlvDecoder._decode_multi_byte_tag(data)
    
    @staticmethod
    def _decode_multi_byte_tag(data: bytes) -> int:
        """
        解码多字节标签
        
        Args:
            data: 标签字节数组
            
        Returns:
            标签值
        """
        result = 0
        for i in range(1, len(data)):
            val = data[i] & 0x7F
            result |= val << ((i - 1) * 7)
        return result
    
    @staticmethod
    def get_value_length(data: bytes) -> int:
        """
        获取值长度

        Args:
            data: 长度字节数组

        Returns:
            值长度
        """
        if len(data) == 0:
            return 0

        first_byte = data[0]
        if (first_byte & 0x80) != 0x80:
            # 短格式 - 按照 C# 版本的逻辑处理所有字节
            result = 0
            for byte in data:
                result = (result << 8) | (byte & 0xFF)
            return result
        else:
            # 长格式 - 按照 C# 版本的逻辑
            result = first_byte & 0x7F
            for i in range(1, len(data)):
                byte = data[i] & 0x7F
                result |= (byte << (i * 7))
            return result
