﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Events.LogEvent
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data.Enums;

#nullable enable
namespace iMooIm.Core.Events;

public class LogEvent
{
  public LogLevel Level = LogLevel.None;
  public string Message = "";
}
