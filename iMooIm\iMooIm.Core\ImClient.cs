﻿using System.Buffers.Text;
using iMooIm.Core.Data;
using iMooIm.Core.Data.Enums;
using iMooIm.Core.Data.Messages;
using iMooIm.Core.Events;
using iMooIm.Core.Tools;

namespace iMooIm.Core;

public class ImClient
{
    public event EventHandler<LogEvent> OnLog;
    private TcpConnection? TcpConnection;
    public void Init()
    {
        OnLog?.Invoke(this,new(){
            Message = "Client Initialized",
            Level = LogLevel.Information
        });
        MessageDecoder.Init();
        OnLog?.Invoke(this,new(){
            Message = "MessageDecoder Initialized",
            Level = LogLevel.Information
        });
        TcpConnection = new();
        TcpConnection.OnLog += (sender, args) => OnLog?.Invoke(sender,args);
        TcpConnection.Init("gw.im.okii.com", 8000);
    }

    public void Login()
    {
        var message= new PublicKeyRequestMessage
        {
            RId = 1000
        };
        TcpConnection?.Send(message.Encode());
        var pubKey = TcpConnection?.GetResponse<PublicKeyResponseMessage>(1000).publicKey;
        OnLog?.Invoke(this,new(){
            Level = LogLevel.Trace,
            Message = "Got encrypt key:"+pubKey
        });
        var setEncryptMessage = new EncryptSetRequestMessage
        {
            RId = 1001,
            encryptKey = EncryptUtil.EncryptKey(TcpConnection.EncryptKey, pubKey),
            encryptType = 1
        };
        TcpConnection?.Send(setEncryptMessage.Encode());
        TcpConnection?.GetResponse<EncryptSetResponseMessage>(1001);
        var registMessage = new RegistRequestMessage()
        {
            RId = 1002,
            AppKey = "3b0ff2264f2240b4a4e866c7cb277ec9",
            BasebandVersion = "I20-V2.3.0-20221118-15.12.02",
            BuildNumber = "OPM1.171019.026 release-keys",
            DeviceId = "anconbjwfhaoanth",
            ModelNumber = "Z6_DFB",
            PkgName = "com.xtc.i3launcher",
            Platform = 8,
            Resolution = "320*360",
            SdkVerison = 27,
            SysName = "Z6_DFB 8.1.0",
        };
        TcpConnection?.Send(registMessage.Encode());
        TcpConnection?.GetResponse<EncryptSetResponseMessage>(1002);
    }
}