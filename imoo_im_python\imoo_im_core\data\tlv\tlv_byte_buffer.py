"""
TLV 字节缓冲区 - 用于处理 TLV 数据流
"""

import io
from typing import Optional


class TlvByteBuffer(io.BytesIO):
    """
    TLV 字节缓冲区，继承自 BytesIO，用于处理 TLV 数据流
    """
    
    def __init__(self, initial_bytes: Optional[bytes] = None):
        super().__init__(initial_bytes)
        self._print_log = False
        self._first_total_size = 0
        self._first_tag_size = 0
        self._first_length_size = 0
    
    def has_next_tlv_data(self) -> bool:
        """
        检查是否还有下一个 TLV 数据
        
        Returns:
            如果有下一个 TLV 数据则返回 True，否则返回 False
        """
        if self.getvalue().__len__() == 0:
            return False
        
        self._compute()
        
        if self._first_total_size > 0 and self.getvalue().__len__() > 0:
            return self._first_total_size <= self.getvalue().__len__()
        
        return False
    
    def reset(self):
        """重置缓冲区"""
        self.seek(0)
        self.truncate(0)
        self._first_total_size = 0
        self._first_tag_size = 0
        self._first_length_size = 0
    
    def cut_next_tlv_data(self) -> Optional[bytes]:
        """
        切出下一个 TLV 数据
        
        Returns:
            下一个 TLV 数据的字节数组，如果没有则返回 None
        """
        if not self.has_next_tlv_data():
            return None
        
        data = self.getvalue()
        data_length = len(data)
        
        if self._first_total_size == data_length:
            # 整个缓冲区就是一个 TLV 数据
            result = data
            self.reset()
            return result
        elif self._first_total_size < data_length:
            # 缓冲区包含多个 TLV 数据
            remaining_data = data[self._first_total_size:]
            result = data[:self._first_total_size]
            
            # 重置并写入剩余数据
            self.reset()
            self.write(remaining_data)
            
            return result
        
        return None
    
    def _compute(self):
        """计算 TLV 数据的各个部分大小"""
        if len(self.getvalue()) > 0:
            self._compute_tag_size()
            self._compute_length_size()
            self._compute_total_size()
    
    def _compute_tag_size(self):
        """计算标签大小"""
        if self._first_tag_size == 0:
            # 这里需要导入 TlvDecoder，但为了避免循环导入，我们先实现基本逻辑
            data = self.getvalue()
            if len(data) > 0:
                self._first_tag_size = self._get_tag_length(data)
    
    def _compute_length_size(self):
        """计算长度字段大小"""
        if self._first_length_size == 0 and self._first_tag_size > 0:
            data = self.getvalue()
            if len(data) > self._first_tag_size:
                self._first_length_size = self._get_length_field_length(data, self._first_tag_size)
    
    def _compute_total_size(self):
        """计算总大小"""
        if (self._first_tag_size > 0 and 
            self._first_length_size > 0 and 
            self._first_total_size == 0):
            
            data = self.getvalue()
            if len(data) >= self._first_tag_size + self._first_length_size:
                length_bytes = data[self._first_tag_size:self._first_tag_size + self._first_length_size]
                value_length = self._get_value_length(length_bytes)
                self._first_total_size = self._first_tag_size + self._first_length_size + value_length
    
    def _get_tag_length(self, data: bytes) -> int:
        """
        获取标签长度
        
        Args:
            data: 数据字节数组
            
        Returns:
            标签长度
        """
        if len(data) == 0:
            return 0
        
        first_byte = data[0]
        if (first_byte & 0x80) != 0x80:
            return 1
        else:
            # 多字节标签
            length = 1
            for i in range(1, len(data)):
                length += 1
                if (data[i] & 0x80) != 0x80:
                    break
            return length
    
    def _get_length_field_length(self, data: bytes, tag_length: int) -> int:
        """
        获取长度字段长度

        Args:
            data: 数据字节数组
            tag_length: 标签长度

        Returns:
            长度字段长度
        """
        if len(data) <= tag_length:
            return 0

        # 按照 C# 版本的逻辑：计算连续的长度字节数
        count = 0
        offset = tag_length
        while offset < len(data):
            count += 1
            if (data[offset] & 0x80) == 0:
                return count
            offset += 1
        return 0
    
    def _get_value_length(self, length_bytes: bytes) -> int:
        """
        获取值长度

        Args:
            length_bytes: 长度字节数组

        Returns:
            值长度
        """
        if len(length_bytes) == 0:
            return 0

        first_byte = length_bytes[0]
        if (first_byte & 0x80) != 0x80:
            # 短格式 - 按照 C# 版本的逻辑处理所有字节
            result = 0
            for byte in length_bytes:
                result = (result << 8) | (byte & 0xFF)
            return result
        else:
            # 长格式 - 按照 C# 版本的逻辑
            result = first_byte & 0x7F
            for i in range(1, len(length_bytes)):
                byte = length_bytes[i] & 0x7F
                result |= (byte << (i * 7))
            return result
