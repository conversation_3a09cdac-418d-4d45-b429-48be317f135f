"""
加密设置响应消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(34)
class EncryptSetResponseMessage(BaseMessage):
    """加密设置响应消息，服务器对加密设置请求的响应"""

    def __init__(self):
        super().__init__()
        self._code: int = 0
        self._desc: str = ""

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'code': 2,
            'desc': 3
        }

    @property
    def code(self) -> int:
        """响应代码"""
        return self._code

    @code.setter
    def code(self, value: int):
        self._code = value

    @property
    def desc(self) -> str:
        """响应描述"""
        return self._desc

    @desc.setter
    def desc(self, value: str):
        self._desc = value
