# iMooIm Python 客户端

这是基于原始 C# 实现的 Python 版本 iMooIm 即时通讯客户端，提供完全一致的功能。

## 功能特性

- ✅ **完整的协议实现**: 与原始 C# 版本保持 100% 兼容
- ✅ **TLV 编码解码**: 完整实现 Tag-Length-Value 协议格式
- ✅ **RSA + XOR 加密**: 支持 RSA 公钥加密和 XOR 对称加密
- ✅ **异步消息处理**: 支持并发消息收发和响应等待
- ✅ **设备注册**: 完整的设备注册和认证流程
- ✅ **日志系统**: 详细的日志记录和调试信息
- ✅ **类型安全**: 使用 Python 类型注解确保代码质量

## 项目结构

```
imoo_im_python/
├── imoo_im_core/                 # 核心库
│   ├── data/                     # 数据模块
│   │   ├── attributes/           # 属性装饰器
│   │   ├── enums/               # 枚举定义
│   │   ├── messages/            # 消息类型
│   │   ├── tlv/                 # TLV 编码解码
│   │   ├── base_message.py      # 基础消息类
│   │   ├── entity.py            # 实体类
│   │   └── message_decoder.py   # 消息解码器
│   ├── events/                  # 事件定义
│   ├── tools/                   # 工具模块
│   ├── im_client.py             # IM 客户端主类
│   └── tcp_connection.py        # TCP 连接管理
├── examples/                    # 示例程序
│   ├── 01_basic_login.py        # 基本登录示例
│   ├── 02_send_message.py       # 发送消息示例
│   ├── 03_message_listener.py   # 消息监听示例
│   ├── 04_custom_config.py      # 自定义配置示例
│   └── README.md               # 示例说明文档
├── main.py                      # 基础示例程序
├── pyproject.toml              # 项目配置
└── README.md                   # 说明文档
```

## 快速开始

### 安装依赖

```bash
# 使用 uv（推荐）
uv sync

# 或使用 pip
pip install -e .
```

### 运行示例

```bash
# 运行基础示例
uv run python main.py

# 或运行详细示例
uv run python examples/01_basic_login.py

# 直接运行（如果已安装依赖）
python main.py
python examples/01_basic_login.py
```

## 使用方法

### 基本用法

```python
from imoo_im_core import ImClient, DeviceInfo, AppInfo
from imoo_im_core.events.log_event import LogEvent

# 创建设备信息
device_info = DeviceInfo()
device_info.baseband_version = "YOUR_BASEBAND_VERSION"
device_info.build_number = "YOUR_BUILD_NUMBER"
device_info.bind_number = "YOUR_BIND_NUMBER"
device_info.chip_id = "YOUR_CHIP_ID"
device_info.account_id = "YOUR_ACCOUNT_ID"
device_info.model_number = "YOUR_MODEL_NUMBER"
device_info.sdk_version = 27
device_info.sys_name = "YOUR_SYS_NAME"
device_info.sys_version = "YOUR_SYS_VERSION"
device_info.eebbk_key = ""

# 创建应用信息
app_info = AppInfo()

# 创建客户端
client = ImClient(device_info, app_info, 0)

# 设置日志回调
def on_log(sender, event: LogEvent):
    print(f"[{event.level.name}] {event.message}")

client.on_log = on_log

try:
    # 初始化并连接
    client.init()

    # 执行登录流程
    client.login()

    print("登录成功！")

finally:
    # 关闭连接
    client.close()
```

### 📚 示例程序

我们提供了多个实用的示例程序，帮助您快速上手：

- **[01_basic_login.py](examples/01_basic_login.py)** - 基本登录示例
- **[02_send_message.py](examples/02_send_message.py)** - 发送消息示例
- **[03_message_listener.py](examples/03_message_listener.py)** - 消息监听示例
- **[04_custom_config.py](examples/04_custom_config.py)** - 自定义配置示例

详细使用说明请查看 [examples/README.md](examples/README.md)

### 配置文件方式（推荐）

对于生产环境，推荐使用配置文件方式管理设备信息：

```bash
# 运行配置示例，首次运行会生成 config.json
python examples/04_custom_config.py

# 编辑生成的配置文件，填入您的实际设备信息
# 再次运行即可
```

## 设备信息配置

在使用客户端之前，您需要准备以下设备信息：

| 字段 | 说明 | 示例 |
|------|------|------|
| `baseband_version` | 基带版本 | "I20-V2.3.0-********-15.12.02" |
| `build_number` | 构建号 | "OPM1.171019.026 release-keys" |
| `bind_number` | 绑定号（设备唯一标识） | "your_unique_bind_number" |
| `chip_id` | 芯片ID | "your_chip_id" |
| `account_id` | 账户ID | "your_account_id" |
| `model_number` | 设备型号 | "Z6_DFB" |
| `sdk_version` | SDK版本 | 27 |
| `sys_name` | 系统名称 | "Z6_DFB 8.1.0" |
| `sys_version` | 系统版本 | "8.1.0" |
| `eebbk_key` | EEBBK密钥 | "" (通常为空) |

⚠️ **重要提示**: 请确保使用真实有效的设备信息，否则可能导致登录失败。



### 开发工具

```bash
# 代码格式化
uv run black .

# 代码排序
uv run isort .

# 类型检查
uv run mypy .

# 运行测试
uv run pytest
```







