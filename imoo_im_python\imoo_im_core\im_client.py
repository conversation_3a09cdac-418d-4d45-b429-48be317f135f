"""
IM 客户端主类 - 提供即时通讯客户端的主要功能
"""

import threading
import time
import json
import re
from datetime import datetime
from typing import Optional, Callable, Any
from .tcp_connection import TcpConnection
from .data.message_decoder import MessageDecoder
from .data.account.device_info import DeviceInfo
from .data.account.app_info import AppInfo
from .data.base_request_param import BaseRequestParam
from .data.chat_message.text_message import TextMessage
from .data.messages.public_key_request_message import PublicKeyRequestMessage
from .data.messages.public_key_response_message import PublicKeyResponseMessage
from .data.messages.encrypt_set_request_message import EncryptSetRequestMessage
from .data.messages.encrypt_set_response_message import EncryptSetResponseMessage
from .data.messages.regist_request_message import RegistRequestMessage
from .data.messages.regist_response_message import RegistResponseMessage
from .data.messages.login_request_message import LoginRequestMessage
from .data.messages.login_response_message import LoginResponseMessage
from .data.messages.account_request_message import AccountRequestMessage
from .data.messages.account_response_message import AccountResponseMessage
from .data.messages.heartbeat_request_message import HeartbeatRequestMessage
from .data.messages.sync_request_message import SyncRequestMessage
# from .data.messages.sync_response_message import SyncResponseMessage  # 暂时注释，需要修复
from .data.messages.sync_finish_ack_request import SyncFinishAckRequest
from .data.messages.push_response_ack_request_message import PushResponseAckRequestMessage
from .data.messages.push_response_message import PushResponseMessage
from .data.messages.single_message_request_message import SingleMessageRequestMessage
from .events.log_event import LogEvent
from .events.receive_message_event import ReceiveMessageEvent
from .events.text_message_event import TextMessageEvent
from .events.save_sync_key_event import SaveSyncKeyEvent
from .data.enums.log_level import LogLevel
from .tools.encrypt_util import EncryptUtil
from .tools.sync_key_manager import SyncKeyManager
from .tools.rid_recorder import RIdRecorder
from .exceptions.account_state_exception import AccountStateException


class ImClient:
    """
    即时通讯客户端主类

    提供连接、登录、消息收发等核心功能
    """

    def __init__(self, device_info: DeviceInfo, app_info: AppInfo, sync_key: int):
        self._tcp_connection: Optional[TcpConnection] = None
        self._heartbeat_thread: Optional[threading.Thread] = None
        self._im_account_id: int = 0
        self._regist_id: int = 0
        self._login_state: bool = False

        # 保存设备和应用信息
        self._device_info = device_info
        self._app_info = app_info
        self._sync_key = sync_key

        # 事件回调
        self.on_log: Optional[Callable[[Any, LogEvent], None]] = None
        self.on_receive_text_message: Optional[Callable[[Any, TextMessageEvent], None]] = None
        self.on_save_sync_key_event: Optional[Callable[[Any, SaveSyncKeyEvent], None]] = None

    def _heartbeat(self):
        """
        心跳线程方法
        """
        while True:
            if self._tcp_connection and self._login_state:
                try:
                    heartbeat_msg = HeartbeatRequestMessage()
                    self._tcp_connection.send_without_check(heartbeat_msg.encode())
                    self._log_debug("发送心跳消息")
                except Exception as e:
                    self._log_error(f"发送心跳失败: {e}")
            time.sleep(60)  # 每60秒发送一次心跳

    def _on_receive_message(self, sender: Any, receive_message_event: ReceiveMessageEvent):
        """
        处理接收到的消息

        Args:
            sender: 发送者
            receive_message_event: 接收消息事件
        """
        try:
            message = receive_message_event.message
            if not isinstance(message, PushResponseMessage):
                return

            # 发送确认消息
            ack_message = PushResponseAckRequestMessage()
            ack_message.r_id = RIdRecorder.get_r_id()
            ack_message.regist_id = self._regist_id
            ack_message.im_account_id = self._im_account_id
            ack_message.sync_key = message.sync_key

            if self._tcp_connection:
                self._tcp_connection.send(ack_message.encode())

            # 处理文本消息
            if message.msg_type == 1:
                text_message_data = json.loads(message.msg.decode('utf-8'))
                text_message = TextMessage()
                text_message.content = text_message_data.get('content', '')

                if self.on_receive_text_message:
                    event = TextMessageEvent()
                    event.content = re.sub(r'\\(.)', r'\1', text_message.content)  # 处理转义字符
                    event.dialog_id = message.dialog_id
                    event.friend_id = message.im_account_id
                    self.on_receive_text_message(self, event)

        except Exception as e:
            self._log_error(f"处理接收消息时出错: {e}")

    def _sync(self):
        """
        同步消息
        """
        try:
            self._log_info("开始消息同步")

            sync_request = SyncRequestMessage()
            sync_request.r_id = RIdRecorder.get_r_id()
            sync_request.regist_id = self._regist_id
            sync_request.im_account_id = self._im_account_id
            sync_request.sync_key = SyncKeyManager.msg_sync_key

            if self._tcp_connection:
                self._tcp_connection.send(sync_request.encode())
                # 暂时跳过同步响应处理
                # sync_response = self._tcp_connection.get_response(sync_request.r_id, SyncResponseMessage)
                sync_response = None

                if sync_response:
                    self._log_info(f"同步完成，收到 {len(sync_response.messages) if hasattr(sync_response, 'messages') else 0} 条消息")

                    # 发送同步完成确认
                    sync_finish_ack = SyncFinishAckRequest()
                    sync_finish_ack.r_id = RIdRecorder.get_r_id()
                    sync_finish_ack.regist_id = self._regist_id
                    sync_finish_ack.im_account_id = self._im_account_id
                    sync_finish_ack.sync_key = sync_response.sync_key if hasattr(sync_response, 'sync_key') else SyncKeyManager.msg_sync_key

                    self._tcp_connection.send(sync_finish_ack.encode())

                    # 更新同步密钥
                    if hasattr(sync_response, 'sync_key'):
                        SyncKeyManager.msg_sync_key = sync_response.sync_key
                        if self.on_save_sync_key_event:
                            self.on_save_sync_key_event(self, SaveSyncKeyEvent(sync_key=sync_response.sync_key))
                else:
                    self._log_warning("同步响应为空")
        except Exception as e:
            self._log_error(f"消息同步失败: {e}")
    
    def init(self):
        """
        初始化客户端
        """
        SyncKeyManager.msg_sync_key = self._sync_key
        self._tcp_connection = TcpConnection()

        # 设置消息解码器日志回调
        MessageDecoder.on_log = self._forward_log

        # 设置TCP连接日志回调
        self._tcp_connection.on_log = self._forward_log

        # 初始化消息解码器
        MessageDecoder.init()

        # 连接到服务器
        self._tcp_connection.init("gw.im.okii.com", 8000)

        self._log_info("客户端已初始化")
    
    def login(self):
        """
        执行登录流程
        """
        # 阶段1: 获取RSA密钥
        public_key_request = PublicKeyRequestMessage()
        public_key_request.r_id = 1000
        self._tcp_connection.send(public_key_request.encode())
        public_key_response = self._tcp_connection.get_response(1000, PublicKeyResponseMessage)

        self._log_debug(f"获取到RSA密钥: {public_key_response.public_key}")

        if public_key_response and public_key_response.code == 200:
            self._log_info("阶段1: 获取RSA密钥 成功")

            # 阶段2: 设置加密
            encrypt_set_request = EncryptSetRequestMessage()
            encrypt_set_request.r_id = 1001
            encrypt_set_request.encrypt_key = EncryptUtil.encrypt_key(
                TcpConnection.encrypt_key,
                public_key_response.public_key
            )
            encrypt_set_request.encrypt_type = 1
            self._tcp_connection.send(encrypt_set_request.encode())
            encrypt_set_response = self._tcp_connection.get_response(1001, EncryptSetResponseMessage)

            if encrypt_set_response and encrypt_set_response.code == 200:
                self._log_info("阶段2: 设置加密 成功")

                # 阶段3: 注册设备
                regist_request = RegistRequestMessage()
                regist_request.r_id = 1002
                regist_request.app_key = self._app_info.app_key  # 使用动态AppKey，与成功的C#版本一致
                regist_request.baseband_version = self._device_info.baseband_version
                regist_request.build_number = self._device_info.build_number
                regist_request.device_id = self._device_info.bind_number
                regist_request.model_number = self._device_info.model_number
                regist_request.pkg_name = "com.xtc.i3launcher"
                regist_request.platform = 8
                regist_request.resolution = "320*360"
                regist_request.sdk_version = self._device_info.sdk_version
                regist_request.sys_name = self._device_info.sys_name
                regist_request.sys_version = self._device_info.sys_version  # 成功的C#版本设置了这个字段！
                # 注意：成功的C#版本没有设置 imei, imsi, mac 字段
                # regist_request.imei = self._device_info.chip_id
                # regist_request.imsi = ""
                # regist_request.mac = ""
                self._tcp_connection.send(regist_request.encode())
                regist_response = self._tcp_connection.get_response(1002, RegistResponseMessage)

                if regist_response and regist_response.code == 200:
                    self._log_info("阶段3: 注册设备 成功")

                    # 阶段4: 登录
                    total_milliseconds = int((datetime.utcnow() - datetime(1970, 1, 1)).total_seconds() * 1000)
                    base_request_param = BaseRequestParam()
                    base_request_param.account_id = self._device_info.account_id
                    base_request_param.device_id = self._device_info.bind_number
                    base_request_param.regist_id = regist_response.regist_id
                    base_request_param.token = self._device_info.chip_id

                    login_request = LoginRequestMessage()
                    login_request.r_id = 1003
                    login_request.regist_id = regist_response.regist_id
                    login_request.regist_token = regist_response.regist_token
                    login_request.platform = 8
                    login_request.sdk_version = 210
                    login_request.im_sdk_version_name = "2.1.0"
                    login_request.timestamp = total_milliseconds
                    login_request.eebbk_key = self._device_info.eebbk_key
                    login_request.base_request_param = json.dumps(base_request_param.__dict__)
                    self._tcp_connection.send(login_request.encode())
                    login_response = self._tcp_connection.get_response(1003, LoginResponseMessage)

                    if login_response and login_response.code == 200:
                        self._log_info("阶段4: 登录 成功")

                        # 阶段5: 设置账户状态为在线
                        account_request = AccountRequestMessage()
                        account_request.app_key = self._app_info.app_key
                        account_request.status = 2
                        account_request.r_id = 1004
                        account_request.regist_id = regist_response.regist_id
                        account_request.business_id = self._device_info.account_id
                        account_request.business_type = 1
                        account_request.business_token = self._device_info.chip_id
                        account_request.low_power = 2
                        self._tcp_connection.send(account_request.encode())
                        account_response = self._tcp_connection.get_response(1004, AccountResponseMessage)

                        if account_response and account_response.code == 200:
                            self._log_info("阶段5: 设置账户状态为在线 成功")

                            self._im_account_id = account_response.im_account_id
                            self._regist_id = regist_response.regist_id

                            if not self._tcp_connection:
                                self._log_critical("TCP连接为空!!!")
                                raise Exception("TCP连接为空")

                            # 设置事件处理器
                            self._tcp_connection.on_receive_message = self._on_receive_message
                            self._tcp_connection.on_need_sync = lambda sender, event: threading.Thread(target=self._sync).start()

                            # 启动心跳线程
                            self._heartbeat_thread = threading.Thread(target=self._heartbeat, daemon=True)
                            self._heartbeat_thread.start()

                            self._login_state = True
                        else:
                            self._log_critical("阶段5: 设置账户状态为在线 失败")
                    else:
                        self._log_critical("阶段4: 登录 失败")
                else:
                    self._log_critical("阶段3: 注册设备 失败")
            else:
                self._log_critical("阶段2: 设置加密 失败")
        else:
            self._log_critical("阶段1: 获取RSA密钥 失败")

    def send_text_msg(self, content: str, dialog_id: int):
        """
        发送文本消息

        Args:
            content: 消息内容
            dialog_id: 对话ID
        """
        if not self._login_state:
            raise AccountStateException("账户未登录")

        try:
            message_request = SingleMessageRequestMessage()
            message_request.r_id = RIdRecorder.get_r_id()
            message_request.regist_id = self._regist_id
            message_request.account_id = self._im_account_id
            message_request.receiver_id = dialog_id
            message_request.content_type = 3
            message_request.msg_type = 1

            # 构造消息内容
            msg_content = {
                "content": content
            }
            message_request.msg = json.dumps(msg_content).encode('utf-8')

            if self._tcp_connection:
                self._tcp_connection.send(message_request.encode())
                self._log_info(f"发送文本消息: {content}")
            else:
                self._log_error("TCP连接为空，无法发送消息")

        except Exception as e:
            self._log_error(f"发送文本消息失败: {e}")
    
    def close(self):
        """关闭客户端连接"""
        if self._tcp_connection:
            self._tcp_connection.close()
            self._tcp_connection = None
        self._log_info("客户端已关闭")
    
    def _forward_log(self, sender: Any, event: LogEvent):
        """转发日志事件"""
        if self.on_log:
            self.on_log(sender, event)
    
    def _log_trace(self, message: str):
        """记录跟踪日志"""
        if self.on_log:
            self.on_log(self, LogEvent(level=LogLevel.TRACE, message=message))
    
    def _log_debug(self, message: str):
        """记录调试日志"""
        if self.on_log:
            self.on_log(self, LogEvent(level=LogLevel.DEBUG, message=message))
    
    def _log_info(self, message: str):
        """记录信息日志"""
        if self.on_log:
            self.on_log(self, LogEvent(level=LogLevel.INFORMATION, message=message))
    
    def _log_warning(self, message: str):
        """记录警告日志"""
        if self.on_log:
            self.on_log(self, LogEvent(level=LogLevel.WARNING, message=message))
    
    def _log_error(self, message: str):
        """记录错误日志"""
        if self.on_log:
            self.on_log(self, LogEvent(level=LogLevel.ERROR, message=message))
    
    def _log_critical(self, message: str):
        """记录严重错误日志"""
        if self.on_log:
            self.on_log(self, LogEvent(level=LogLevel.CRITICAL, message=message))
