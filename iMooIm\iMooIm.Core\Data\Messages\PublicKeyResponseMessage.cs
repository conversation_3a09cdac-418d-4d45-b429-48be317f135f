﻿using iMooIm.Core.Data.Attributes;

namespace iMooIm.Core.Data.Messages;
[CommandId(23)]
public class PublicKeyResponseMessage : BaseMessage
{
    [TagId(2)]
    public int Code { get; set; }
    [TagId(3)]
    public string Desc { get; set; }
    [TagId(12)]
    public byte[] Exponent { get; set; }
    [TagId(11)]
    public byte[] Modulus { get; set; }
    [TagId(10)]
    public string publicKey { get; set; }
}