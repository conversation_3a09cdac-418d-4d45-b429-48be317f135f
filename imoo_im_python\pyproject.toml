[project]
name = "imoo-im-python"
version = "0.1.0"
description = "Python 版本的 iMooIm 即时通讯客户端，基于原始 C# 实现移植"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "cryptography>=45.0.5",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["imoo_im_core"]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
