﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Message.Response.SyncFinishResponseMessage
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;

#nullable enable
namespace iMooIm.Core.Message.Response;

[CommandId(14)]
internal class SyncFinishResponseMessage : BaseMessage
{
  [TagId(2)]
  public int Code { get; set; }

  [TagId(3)]
  public string Desc { get; set; } = "";

  [TagId(11)]
  public long ImAccountId { get; set; }

  [TagId(12)]
  public long RegistId { get; set; }

  [TagId(13)]
  public long SyncKey { get; set; }
}
