﻿namespace iMooIm.Core.Data.Tlv;

public class TLVDecoder {

    private static bool isDebugEnabled;

    public static TLVDecodeResult Decode(byte[] data) {
        return DecodeTLV(data);
    }

    private static TLVDecodeResult DecodeTLV(byte[] data) {
        if (data.Length == 0) {
            return new TLVDecodeResult();
        }
        int tagLength = GetTagLength(data);
        byte[] tagBytes = new byte[tagLength];
        Array.Copy(data, 0, tagBytes, 0, tagLength);

        int lengthFieldLength = GetLengthFieldLength(data, tagLength);
        byte[] lengthBytes = new byte[lengthFieldLength];
        Array.Copy(data, tagLength, lengthBytes, 0, lengthFieldLength);
        int valueLength = GetValueLength(lengthBytes);
        byte[] valueBytes = new byte[valueLength];
        Array.Copy(data, tagLength + lengthFieldLength, valueBytes, 0, valueLength);

        object value = valueBytes;
        if (GetDataType(tagBytes) == 32) {
            value = DecodeNestedTLV(valueBytes);
        }

        TLVDecodeResult result = new TLVDecodeResult();
        result.FrameType = GetFrameType(tagBytes);
        result.DataType = (int)GetDataType(tagBytes);
        result.TagValue = GetTagValue(tagBytes);
        result.Length = GetValueLength(lengthBytes);
        result.Value = value;

        return result;
    }

    public static List<TLVDecodeResult> DecodeNestedTLV(byte[] data) {
        if (data == null || data.Length == 0) {
            return null;
        }

        TlvByteBuffer buffer = new TlvByteBuffer();
        buffer.Write(data);
        List<TLVDecodeResult> results = new List<TLVDecodeResult>();

        while (buffer.HasNextTlvData()) {
            results.Add(DecodeTLV(buffer.CutNextTlvData()));
        }

        return results;
    }

    public static int GetTotalTLVCount(byte[] data) {
        int tagLength = GetTagLength(data);
        byte[] tagBytes = new byte[tagLength];
        Array.Copy(data, 0, tagBytes, 0, tagLength);

        int lengthFieldLength = GetLengthFieldLength(data, tagLength);
        byte[] lengthBytes = new byte[lengthFieldLength];
        Array.Copy(data, tagLength, lengthBytes, 0, lengthFieldLength);

        int valueLength = GetValueLength(lengthBytes);
        byte[] valueBytes = new byte[valueLength];
        int offset = tagLength + lengthFieldLength;
        Array.Copy(data, offset, valueBytes, 0, valueLength);

        int dataType = (int)GetDataType(tagBytes);
        int count = dataType == 32 ? 1 + GetTotalTLVCount(valueBytes) : 1;

        int remainingLength = data.Length - (offset + valueLength);
        if (remainingLength > 0) {
            byte[] remainingData = new byte[remainingLength];
            Array.Copy(data, offset + valueLength, remainingData, 0, remainingLength);
            count += GetTotalTLVCount(remainingData);
        }

        return count;
    }

    public static int GetTagLength(byte[] data) {
        int count = 0;
        foreach (var b in data) {
            count++;
            if ((b & 0x80) == 0) {
                return count;
            }
        }
        return 0;
    }

    public static int GetLengthFieldLength(byte[] data, int offset) {
        int count = 0;
        while (offset < data.Length) {
            count++;
            if ((data[offset] & 0x80) == 0) {
                return count;
            }
            offset++;
        }
        return 0;
    }

    public static int GetFrameType(byte[] data) {
        return data[0] & 0x40;
    }

    public static uint GetDataType(byte[] data) {
        return ((uint)data[0]) & 32;
    }

    public static int GetTagValue(byte[] data) {
        if ((data[0] & 0x80) != 0x80) {
            return data[0] & 0x1F;
        } else {
            return DecodeMultiByteTag(data);
        }
    }

    private static int DecodeMultiByteTag(byte[] data) {
        int result = 0;
        for (int i = 1; i < data.Length; i++) {
            int val = data[i] & 0x7F;
            result |= val << ((i - 1) * 7);
        }
        return result;
    }

    public static int GetValueLength(byte[] data) {
        if ((data[0] & 0x80) != 0x80) {
            long result = 0;
            foreach (var b in data) {
                result = (result << 8) | (b & 0xFFL);
            }
            return (int) result;
        } else {
            int result = data[0] & 0x7F;
            for (int i = 1; i < data.Length; i++) {
                int b = data[i] & 0x7F;
                result |= (b << (i * 7));
            }
            return result;
        }
    }
}