﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Message.Request.AccountRequestMessage
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;

#nullable enable
namespace iMooIm.Core.Message.Request;

[CommandId(5)]
internal class AccountRequestMessage : BaseMessage
{
  [TagId(15)]
  public string AppKey { get; set; } = "";

  [TagId(12)]
  public string BusinessId { get; set; } = "";

  [TagId(13)]
  public string BusinessToken { get; set; } = "";

  [TagId(11)]
  public int BusinessType { get; set; }

  [TagId(16 /*0x10*/)]
  public int LowPower { get; set; }

  [TagId(10)]
  public long RegistId { get; set; }

  [TagId(14)]
  public int Status { get; set; }
}
