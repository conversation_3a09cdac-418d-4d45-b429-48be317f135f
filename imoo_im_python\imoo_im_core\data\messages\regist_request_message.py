"""
注册请求消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(1)
class RegistRequestMessage(BaseMessage):
    """注册请求消息，用于向服务器注册设备"""
    
    def __init__(self):
        super().__init__()
        self._app_key: str = ""
        self._baseband_version: str = ""
        self._build_number: str = ""
        self._device_id: str = ""
        self._imei: str = ""
        self._imsi: str = ""
        self._mac: str = ""
        self._model_number: str = ""
        self._pkg_name: str = ""
        self._platform: int = 0
        self._resolution: str = ""
        self._sdk_version: int = 0
        self._sys_name: str = ""
        self._sys_version: str = ""

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'platform': 10,
            'app_key': 11,
            'pkg_name': 12,
            'device_id': 13,
            'sdk_version': 14,
            'sys_name': 15,
            'sys_version': 16,
            'imei': 17,
            'imsi': 18,
            'mac': 19,
            'model_number': 20,
            'baseband_version': 21,
            'build_number': 22,
            'resolution': 23
        }
    
    @property
    def app_key(self) -> str:
        """应用密钥"""
        return self._app_key

    @app_key.setter
    def app_key(self, value: str):
        self._app_key = value

    @property
    def baseband_version(self) -> str:
        """基带版本"""
        return self._baseband_version

    @baseband_version.setter
    def baseband_version(self, value: str):
        self._baseband_version = value

    @property
    def build_number(self) -> str:
        """构建号"""
        return self._build_number

    @build_number.setter
    def build_number(self, value: str):
        self._build_number = value

    @property
    def device_id(self) -> str:
        """设备 ID"""
        return self._device_id

    @device_id.setter
    def device_id(self, value: str):
        self._device_id = value
    
    @property
    def imei(self) -> str:
        """IMEI"""
        return self._imei

    @imei.setter
    def imei(self, value: str):
        self._imei = value

    @property
    def imsi(self) -> str:
        """IMSI"""
        return self._imsi

    @imsi.setter
    def imsi(self, value: str):
        self._imsi = value

    @property
    def mac(self) -> str:
        """MAC 地址"""
        return self._mac

    @mac.setter
    def mac(self, value: str):
        self._mac = value

    @property
    def model_number(self) -> str:
        """型号"""
        return self._model_number

    @model_number.setter
    def model_number(self, value: str):
        self._model_number = value

    @property
    def pkg_name(self) -> str:
        """包名"""
        return self._pkg_name

    @pkg_name.setter
    def pkg_name(self, value: str):
        self._pkg_name = value

    @property
    def platform(self) -> int:
        """平台"""
        return self._platform

    @platform.setter
    def platform(self, value: int):
        self._platform = value

    @property
    def resolution(self) -> str:
        """分辨率"""
        return self._resolution

    @resolution.setter
    def resolution(self, value: str):
        self._resolution = value

    @property
    def sdk_version(self) -> int:
        """SDK 版本"""
        return self._sdk_version

    @sdk_version.setter
    def sdk_version(self, value: int):
        self._sdk_version = value

    @property
    def sys_name(self) -> str:
        """系统名称"""
        return self._sys_name

    @sys_name.setter
    def sys_name(self, value: str):
        self._sys_name = value

    @property
    def sys_version(self) -> str:
        """系统版本"""
        return self._sys_version

    @sys_version.setter
    def sys_version(self, value: str):
        self._sys_version = value
