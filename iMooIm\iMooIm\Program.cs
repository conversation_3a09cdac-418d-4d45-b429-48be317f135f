﻿using System.Text.Json;
using iMooIm.Core;
using iMooIm.Core.Data;
using iMooIm.Core.Data.Messages;
using iMooIm.Core.Data.Tlv;
using iMooIm.Tools;
using Microsoft.Extensions.Logging;

using ILoggerFactory factory = LoggerFactory.Create(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
var logger = factory.CreateLogger("iMooIm");
var coreLogger = factory.CreateLogger("iMooIm-Core");
var client = new ImClient();
logger.LogInformation("Starting iMooIm");
client.OnLog += (sender, @event) =>
{
    coreLogger.Log((LogLevel)@event.Level,@event.Message);
};
client.Init();
client.Login();
//Console.WriteLine(TLVDecoder.Decode([0xA0,0x22,0x08,0x01,0x02,0x03,0xE9,0x02,0x02,0xC8]));