# iMooIm Python 客户端示例

这个文件夹包含了多个实用的示例程序，帮助您快速上手 iMooIm Python 客户端。

## 📁 示例列表

### 01_basic_login.py - 基本登录示例
**功能**: 演示最基本的连接和登录操作
**适用场景**: 
- 初次使用 iMooIm 客户端
- 验证设备信息配置是否正确
- 测试基本连接功能

**使用方法**:
1. 编辑文件中的 `create_device_info()` 函数
2. 填入您的实际设备信息
3. 运行程序验证登录是否成功

### 02_send_message.py - 发送消息示例
**功能**: 演示如何发送文本消息给好友
**适用场景**:
- 需要主动发送消息
- 测试消息发送功能
- 批量消息发送

**使用方法**:
1. 完成设备信息配置
2. 在 `friend_config` 中填入好友信息
3. 运行程序发送测试消息

### 03_message_listener.py - 消息监听示例
**功能**: 持续监听和处理接收到的消息
**适用场景**:
- 聊天机器人开发
- 消息自动处理
- 长时间运行的监听程序

**特色功能**:
- 优雅的程序关闭（Ctrl+C）
- 消息统计和日志记录
- 可扩展的消息处理逻辑
- 自动回复框架

### 04_custom_config.py - 自定义配置示例
**功能**: 使用配置文件管理设备信息和应用设置
**适用场景**:
- 生产环境部署
- 多环境配置管理
- 团队协作开发

**特色功能**:
- JSON 配置文件管理
- 配置验证和默认值
- 灵活的日志配置
- 功能开关控制

## 🚀 快速开始

### 1. 准备设备信息
在使用任何示例之前，您需要准备以下设备信息：

```python
device_info.baseband_version = "I20-V2.3.0-********-15.12.02"  # 基带版本
device_info.build_number = "OPM1.171019.026 release-keys"      # 构建号
device_info.bind_number = "your_bind_number"                   # 绑定号（设备唯一标识）
device_info.chip_id = "your_chip_id"                          # 芯片ID
device_info.account_id = "your_account_id"                    # 账户ID
device_info.model_number = "Z6_DFB"                           # 型号
device_info.sys_name = "Z6_DFB 8.1.0"                        # 系统名称
device_info.sys_version = "8.1.0"                            # 系统版本
```

### 2. 运行示例

```bash
# 进入项目目录
cd imoo_im_python

# 运行基本登录示例
uv run python examples/01_basic_login.py

# 或使用 Python 直接运行
python examples/01_basic_login.py
```

### 3. 使用配置文件（推荐）

对于生产环境，推荐使用配置文件方式：

```bash
# 运行配置示例，首次运行会生成 config.json
python examples/04_custom_config.py

# 编辑生成的配置文件
# 填入您的实际设备信息

# 再次运行
python examples/04_custom_config.py
```

## 📝 配置文件示例

使用 `04_custom_config.py` 时会生成的配置文件格式：

```json
{
  "device_info": {
    "baseband_version": "YOUR_BASEBAND_VERSION",
    "build_number": "YOUR_BUILD_NUMBER",
    "bind_number": "YOUR_BIND_NUMBER",
    "chip_id": "YOUR_CHIP_ID",
    "account_id": "YOUR_ACCOUNT_ID",
    "model_number": "YOUR_MODEL_NUMBER",
    "sdk_version": 27,
    "sys_name": "YOUR_SYS_NAME",
    "sys_version": "YOUR_SYS_VERSION",
    "eebbk_key": ""
  },
  "connection": {
    "server_host": "gw.im.okii.com",
    "server_port": 8000,
    "timeout": 30
  },
  "logging": {
    "level": "INFO",
    "file_logging": false,
    "log_file": "imoo_client.log"
  }
}
```

## 🔧 自定义开发

### 扩展消息处理
在 `03_message_listener.py` 的 `process_message()` 方法中添加您的业务逻辑：

```python
def process_message(self, event: TextMessageEvent):
    content = event.content.lower().strip()
    
    # 添加您的处理逻辑
    if "关键词" in content:
        # 执行相应操作
        pass
```

### 添加自动回复
参考 `02_send_message.py` 中的 `send_text_message()` 函数来实现自动回复。

### 消息存储
您可以在消息事件处理器中添加数据库存储逻辑：

```python
def on_text_message(self, sender, event: TextMessageEvent):
    # 存储到数据库
    save_message_to_db(event)
    
    # 其他处理逻辑
    self.process_message(event)
```

## ⚠️ 注意事项

1. **设备信息安全**: 请妥善保管您的设备信息，不要在公开场合分享
2. **网络连接**: 确保网络连接正常，防火墙允许相关端口
3. **错误处理**: 示例中包含了基本的错误处理，生产环境请根据需要完善
4. **资源管理**: 长时间运行的程序请注意资源管理和内存使用

## 🆘 常见问题

### Q: 登录失败怎么办？
A: 检查以下几点：
- 设备信息是否正确填写
- 网络连接是否正常
- 服务器地址和端口是否正确
- 防火墙设置是否允许连接

### Q: 收不到消息怎么办？
A: 确认：
- 登录是否成功
- 事件处理器是否正确绑定
- 程序是否持续运行

### Q: 发送消息失败怎么办？
A: 检查：
- 好友ID是否正确
- 登录状态是否正常
- 消息格式是否正确

## 📚 更多资源

- [主项目 README](../README.md)
- [API 文档](../imoo_im_core/)
- [问题反馈](../../issues)

---

如果您在使用过程中遇到问题，请查看日志输出或提交 Issue。
