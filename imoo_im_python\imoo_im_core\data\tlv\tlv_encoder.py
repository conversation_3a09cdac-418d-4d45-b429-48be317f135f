"""
TLV 编码器 - 将数据编码为 TLV 格式
"""

import math
from typing import List


class TlvEncoder:
    """TLV 编码器"""
    
    @staticmethod
    def encode_tlv(tag: int, length: int, value: bytes) -> bytes:
        """
        编码 TLV 数据
        
        Args:
            tag: 标签值
            length: 长度值
            value: 数据值
            
        Returns:
            编码后的 TLV 字节数组
        """
        tag_bytes = TlvEncoder._encode_tag(0, 0, tag)
        length_bytes = TlvEncoder._encode_length(length)
        
        result = bytearray()
        result.extend(tag_bytes)
        result.extend(length_bytes)
        result.extend(value)
        
        return bytes(result)
    
    @staticmethod
    def encode_nested_tlv(data_type: int, tag: int, length: int, value: bytes) -> bytes:
        """
        编码嵌套 TLV 数据
        
        Args:
            data_type: 数据类型
            tag: 标签值
            length: 长度值
            value: 数据值
            
        Returns:
            编码后的嵌套 TLV 字节数组
        """
        tag_bytes = TlvEncoder._encode_tag(data_type, 0, tag)
        length_bytes = TlvEncoder._encode_length(length)
        
        result = bytearray()
        result.extend(tag_bytes)
        result.extend(length_bytes)
        result.extend(value)
        
        return bytes(result)
    
    @staticmethod
    def _encode_length(length: int) -> bytes:
        """
        编码长度字段
        
        Args:
            length: 长度值
            
        Returns:
            编码后的长度字节数组
        """
        if length < 0:
            raise ValueError("the length must not less than 0.")
        
        if length < 128:
            return bytes([length & 127])
        
        calc_result = math.ceil(math.log(length + 1.0) / math.log(128.0))
        encoded_value = TlvEncoder._encode_variable_length_integer(0, calc_result, length)
        return TlvEncoder._length_encode(encoded_value, calc_result)
    
    @staticmethod
    def _length_encode(value: int, length: int) -> bytes:
        """
        编码长度值
        
        Args:
            value: 要编码的值
            length: 字节长度
            
        Returns:
            编码后的字节数组
        """
        result = bytearray(length)
        result[0] = (value >> ((length - 1) * 8)) & 255
        
        for i in range(1, length):
            result[i] = (value >> (((length - i) - 1) * 8)) & 255
        
        return bytes(result)
    
    @staticmethod
    def _encode_variable_length_integer(base: int, length: int, value: int) -> int:
        """
        编码可变长度整数
        
        Args:
            base: 基础值
            length: 长度
            value: 要编码的值
            
        Returns:
            编码后的整数
        """
        i = 0
        result = base
        
        while True:
            length_minus_1 = length - 1
            if i >= length_minus_1:
                return result | ((value >> (length_minus_1 * 7)) & 127)
            
            result |= (((value >> (i * 7)) & 127) | 128) << ((length_minus_1 - i) * 8)
            i += 1
    
    @staticmethod
    def _encode_tag(data_type: int, constructed: int, tag: int) -> bytes:
        """
        编码标签字段
        
        Args:
            data_type: 数据类型
            constructed: 构造标志
            tag: 标签值
            
        Returns:
            编码后的标签字节数组
        """
        combined = data_type | constructed
        tag_value = combined | tag
        
        if tag >= 31:
            length = math.ceil(math.log(tag + 1.0) / math.log(128.0))
            encoded_value = TlvEncoder._encode_variable_length_integer(
                (combined | 128) << (length * 8), length, tag
            )
        else:
            encoded_value = tag_value
            length = 0
        
        return TlvEncoder._int_to_bytes(encoded_value, length)
    
    @staticmethod
    def _int_to_bytes(value: int, extra_length: int) -> bytes:
        """
        将整数转换为字节数组

        Args:
            value: 要转换的整数
            extra_length: 额外长度

        Returns:
            转换后的字节数组
        """
        if extra_length == 0:
            return bytes([value & 0xFF])

        # 计算需要的字节数
        byte_count = extra_length + 1
        result = bytearray(byte_count)

        # 按照C#版本的正确逻辑实现
        result[0] = (value >> (extra_length * 8)) & 0xFF
        for i in range(byte_count):
            result[i] = (value >> ((extra_length - i) * 8)) & 0xFF

        return bytes(result)
