"""
iMooIm Core - Python 版本的即时通讯客户端核心库

这是基于原始 C# 实现的 Python 移植版本，提供完全一致的功能。

主要特性：
- 完整的 TCP 连接管理
- TLV 协议支持
- 消息编码解码
- 加密通信
- 事件驱动架构
- 完整的登录流程
- 消息同步
- 心跳机制

使用示例：
    from imoo_im_core import ImClient
    from imoo_im_core.data.account import DeviceInfo, AppInfo

    device_info = DeviceInfo()
    app_info = AppInfo()
    client = ImClient(device_info, app_info, 0)
    client.init()
    client.login()
"""

__version__ = "2.0.0"
__author__ = "iMooIm Python Port"

from .im_client import ImClient
from .tcp_connection import TcpConnection
from .data.account import DeviceInfo, AppInfo

__all__ = ["ImClient", "TcpConnection", "DeviceInfo", "AppInfo"]
