﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Tlv.TlvByteBuffer
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using System;
using System.IO;

#nullable enable
namespace iMooIm.Core.Tlv;

internal class TlvByteBuffer : MemoryStream
{
  private volatile int _firstTotalSize = 0;
  private volatile int _firstTagSize = 0;
  private volatile int _firstLengthSize = 0;

  public bool HasNextTlvData()
  {
    bool flag = false;
    if (this.Length == 0L)
      return false;
    this.Compute();
    if (this._firstTotalSize > 0 && this.Length > 0L && (long) this._firstTotalSize <= this.Length)
      flag = true;
    return flag;
  }

  public void Reset()
  {
    this.SetLength(0L);
    this._firstTotalSize = 0;
    this._firstTagSize = 0;
    this._firstLengthSize = 0;
  }

  public override void Write(byte[] bArr, int i, int i2) => base.Write(bArr, i, i2);

  public byte[] CutNextTlvData()
  {
    byte[] numArray1 = Array.Empty<byte>();
    if ((long) this._firstTotalSize == this.Length)
    {
      numArray1 = this.ToArray();
      this.Reset();
    }
    else if ((long) this._firstTotalSize < this.Length)
    {
      byte[] numArray2 = new byte[this.Length - (long) this._firstTotalSize];
      byte[] destinationArray = new byte[this._firstTotalSize];
      Array.Copy((Array) this.ToArray(), this._firstTotalSize, (Array) numArray2, 0, numArray2.Length);
      Array.Copy((Array) this.ToArray(), 0, (Array) destinationArray, 0, destinationArray.Length);
      this.Reset();
      this.Write(numArray2, 0, numArray2.Length);
      numArray1 = destinationArray;
    }
    return numArray1;
  }

  private void Compute()
  {
    if (this.Length <= 0L)
      return;
    this.ComputeTagSize();
    this.ComputeLengthSize();
    this.ComputeTotalSize();
  }

  private void ComputeTagSize()
  {
    if (this._firstTagSize != 0)
      return;
    this._firstTagSize = TLVDecoder.GetTagLength(this.ToArray());
  }

  private void ComputeLengthSize()
  {
    if (this._firstLengthSize != 0 || this._firstTagSize == 0)
      return;
    this._firstLengthSize = TLVDecoder.GetLengthFieldLength(this.ToArray(), this._firstTagSize);
  }

  private void ComputeTotalSize()
  {
    if (this._firstTagSize <= 0 || this._firstLengthSize <= 0 || this._firstTotalSize != 0)
      return;
    byte[] numArray = new byte[this._firstLengthSize];
    Array.Copy((Array) this.ToArray(), this._firstTagSize, (Array) numArray, 0, this._firstLengthSize);
    this._firstTotalSize = this._firstTagSize + this._firstLengthSize + TLVDecoder.GetValueLength(numArray);
  }
}
