﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Tools.SyncKeyManager
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

#nullable disable
namespace iMooIm.Core.Tools;

internal static class SyncKeyManager
{
  internal static long ServerSyncKey { get; set; }

  internal static long MsgSyncKey { get; set; }
}
