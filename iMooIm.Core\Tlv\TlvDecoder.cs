﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Tlv.TLVDecoder
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using System;
using System.Collections.Generic;

#nullable enable
namespace iMooIm.Core.Tlv;

internal class TLVDecoder
{
  internal static TLVDecodeResult Decode(byte[] data) => TLVDecoder.DecodeTLV(data);

  internal static TLVDecodeResult DecodeTLV(byte[] data)
  {
    if (data.Length == 0)
      return new TLVDecodeResult();
    int tagLength = TLVDecoder.GetTagLength(data);
    byte[] numArray1 = new byte[tagLength];
    Array.Copy((Array) data, 0, (Array) numArray1, 0, tagLength);
    int lengthFieldLength = TLVDecoder.GetLengthFieldLength(data, tagLength);
    byte[] numArray2 = new byte[lengthFieldLength];
    Array.Copy((Array) data, tagLength, (Array) numArray2, 0, lengthFieldLength);
    int valueLength = TLVDecoder.GetValueLength(numArray2);
    byte[] numArray3 = new byte[valueLength];
    Array.Copy((Array) data, tagLength + lengthFieldLength, (Array) numArray3, 0, valueLength);
    object obj = (object) numArray3;
    if (TLVDecoder.GetDataType(numArray1) == 32U /*0x20*/)
      obj = (object) TLVDecoder.DecodeNestedTLV(numArray3);
    return new TLVDecodeResult()
    {
      FrameType = TLVDecoder.GetFrameType(numArray1),
      DataType = (int) TLVDecoder.GetDataType(numArray1),
      TagValue = TLVDecoder.GetTagValue(numArray1),
      Length = TLVDecoder.GetValueLength(numArray2),
      Value = obj
    };
  }

  private static List<TLVDecodeResult> DecodeNestedTLV(byte[] data)
  {
    if (data.Length == 0)
      return new List<TLVDecodeResult>();
    TlvByteBuffer tlvByteBuffer = new TlvByteBuffer();
    tlvByteBuffer.Write((ReadOnlySpan<byte>) data);
    List<TLVDecodeResult> tlvDecodeResultList = new List<TLVDecodeResult>();
    while (tlvByteBuffer.HasNextTlvData())
      tlvDecodeResultList.Add(TLVDecoder.DecodeTLV(tlvByteBuffer.CutNextTlvData()));
    return tlvDecodeResultList;
  }

  internal static int GetTotalTLVCount(byte[] data)
  {
    int tagLength = TLVDecoder.GetTagLength(data);
    byte[] numArray1 = new byte[tagLength];
    Array.Copy((Array) data, 0, (Array) numArray1, 0, tagLength);
    int lengthFieldLength = TLVDecoder.GetLengthFieldLength(data, tagLength);
    byte[] numArray2 = new byte[lengthFieldLength];
    Array.Copy((Array) data, tagLength, (Array) numArray2, 0, lengthFieldLength);
    int valueLength = TLVDecoder.GetValueLength(numArray2);
    byte[] numArray3 = new byte[valueLength];
    int sourceIndex = tagLength + lengthFieldLength;
    Array.Copy((Array) data, sourceIndex, (Array) numArray3, 0, valueLength);
    int totalTlvCount = TLVDecoder.GetDataType(numArray1) == 32U /*0x20*/ ? 1 + TLVDecoder.GetTotalTLVCount(numArray3) : 1;
    int length = data.Length - (sourceIndex + valueLength);
    if (length > 0)
    {
      byte[] numArray4 = new byte[length];
      Array.Copy((Array) data, sourceIndex + valueLength, (Array) numArray4, 0, length);
      totalTlvCount += TLVDecoder.GetTotalTLVCount(numArray4);
    }
    return totalTlvCount;
  }

  internal static int GetTagLength(byte[] data)
  {
    int tagLength = 0;
    foreach (byte num in data)
    {
      ++tagLength;
      if (((int) num & 128 /*0x80*/) == 0)
        return tagLength;
    }
    return 0;
  }

  internal static int GetLengthFieldLength(byte[] data, int offset)
  {
    int lengthFieldLength = 0;
    for (; offset < data.Length; ++offset)
    {
      ++lengthFieldLength;
      if (((int) data[offset] & 128 /*0x80*/) == 0)
        return lengthFieldLength;
    }
    return 0;
  }

  private static int GetFrameType(byte[] data) => (int) data[0] & 64 /*0x40*/;

  private static uint GetDataType(byte[] data) => (uint) data[0] & 32U /*0x20*/;

  private static int GetTagValue(byte[] data)
  {
    return ((int) data[0] & 128 /*0x80*/) != 128 /*0x80*/ ? (int) data[0] & 31 /*0x1F*/ : TLVDecoder.DecodeMultiByteTag(data);
  }

  private static int DecodeMultiByteTag(byte[] data)
  {
    int num1 = 0;
    for (int index = 1; index < data.Length; ++index)
    {
      int num2 = (int) data[index] & (int) sbyte.MaxValue;
      num1 |= num2 << (index - 1) * 7;
    }
    return num1;
  }

  internal static int GetValueLength(byte[] data)
  {
    if (((int) data[0] & 128 /*0x80*/) != 128 /*0x80*/)
    {
      long valueLength = 0;
      foreach (byte num in data)
        valueLength = valueLength << 8 | (long) num & (long) byte.MaxValue;
      return (int) valueLength;
    }
    int valueLength1 = (int) data[0] & (int) sbyte.MaxValue;
    for (int index = 1; index < data.Length; ++index)
    {
      int num = (int) data[index] & (int) sbyte.MaxValue;
      valueLength1 |= num << index * 7;
    }
    return valueLength1;
  }
}
