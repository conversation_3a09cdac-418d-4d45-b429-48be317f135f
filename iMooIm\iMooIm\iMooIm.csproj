﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\iMooIm.Core\iMooIm.Core.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="BouncyCastle.Cryptography" Version="2.5.1" />
      <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.1" />
      <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.1" />
    </ItemGroup>

</Project>
