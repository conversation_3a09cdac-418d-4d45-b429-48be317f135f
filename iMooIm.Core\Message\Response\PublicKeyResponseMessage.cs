﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Message.Response.PublicKeyResponseMessage
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;
using System;

#nullable enable
namespace iMooIm.Core.Message.Response;

[CommandId(23)]
internal class PublicKeyResponseMessage : BaseMessage
{
  [TagId(2)]
  public int Code { get; set; }

  [TagId(3)]
  public string Desc { get; set; } = "";

  [TagId(12)]
  public byte[] Exponent { get; set; } = Array.Empty<byte>();

  [TagId(11)]
  public byte[] Modulus { get; set; } = Array.Empty<byte>();

  [TagId(10)]
  public string PublicKey { get; set; } = "";
}
