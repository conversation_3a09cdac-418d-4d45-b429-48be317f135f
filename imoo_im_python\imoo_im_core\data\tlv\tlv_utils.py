"""
TLV 工具类 - 提供字节数组和整数之间的转换功能
"""


def bytes_to_long(data: bytes) -> int:
    """
    将字节数组转换为长整数
    
    Args:
        data: 要转换的字节数组
        
    Returns:
        转换后的长整数
        
    Raises:
        ValueError: 当字节数组长度不正确时
    """
    length = len(data)
    
    if length == 1:
        return data[0] & 0xFF
    elif length == 2:
        return ((data[0] & 0xFF) << 8) | (data[1] & 0xFF)
    elif length == 4:
        return (((data[0] & 0xFF) << 24) |
                ((data[1] & 0xFF) << 16) |
                ((data[2] & 0xFF) << 8) |
                (data[3] & 0xFF))
    elif length == 8:
        return (((data[0] & 0xFF) << 56) |
                ((data[1] & 0xFF) << 48) |
                ((data[2] & 0xFF) << 40) |
                ((data[3] & 0xFF) << 32) |
                ((data[4] & 0xFF) << 24) |
                ((data[5] & 0xFF) << 16) |
                ((data[6] & 0xFF) << 8) |
                (data[7] & 0xFF))
    else:
        raise ValueError("the length of byte array is incorrect.")


def long_to_bytes(value: int) -> bytes:
    """
    将长整数转换为字节数组
    
    Args:
        value: 要转换的长整数
        
    Returns:
        转换后的字节数组
    """
    if value == (value & 0xFF):
        return _long_to_bytes_with_length(value, 1)
    elif value == (value & 0xFFFF):
        return _long_to_bytes_with_length(value, 2)
    elif value == (value & 0xFFFFFFFF):
        return _long_to_bytes_with_length(value, 4)
    else:
        return _long_to_bytes_with_length(value, 8)


def _long_to_bytes_with_length(value: int, length: int) -> bytes:
    """
    将长整数转换为指定长度的字节数组
    
    Args:
        value: 要转换的长整数
        length: 字节数组长度
        
    Returns:
        转换后的字节数组
    """
    result = bytearray(length)
    for i in range(length):
        result[i] = (value >> (((length - i) - 1) * 8)) & 0xFF
    return bytes(result)
