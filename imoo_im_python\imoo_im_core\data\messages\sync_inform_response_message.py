"""
同步通知响应消息
"""

from ..base_message import BaseMessageWithoutRid
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(11)
class SyncInformResponseMessage(BaseMessageWithoutRid):
    """
    同步通知响应消息类

    服务器发送的同步通知
    """

    def __init__(self):
        super().__init__()
        self._im_account_id: int = 0
        self._sync_key: int = 0

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'im_account_id': 11,
            'sync_key': 12
        }

    @property
    def im_account_id(self) -> int:
        """IM账户ID"""
        return self._im_account_id

    @im_account_id.setter
    def im_account_id(self, value: int):
        self._im_account_id = value

    @property
    def sync_key(self) -> int:
        """同步密钥"""
        return self._sync_key

    @sync_key.setter
    def sync_key(self, value: int):
        self._sync_key = value
