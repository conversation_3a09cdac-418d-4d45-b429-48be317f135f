#!/usr/bin/env python3
"""
发送消息示例

这个示例展示了如何在登录后发送文本消息给好友。
您需要先完成登录，然后指定好友信息来发送消息。
"""

import logging
import sys
import time
import json
import uuid
from imoo_im_core.im_client import ImClient
from imoo_im_core.data.account.device_info import DeviceInfo
from imoo_im_core.data.account.app_info import AppInfo
from imoo_im_core.events.log_event import LogEvent
from imoo_im_core.events.text_message_event import TextMessageEvent
from imoo_im_core.data.enums.log_level import LogLevel
from imoo_im_core.data.messages.single_message_request_message import SingleMessageRequestMessage
from imoo_im_core.tools.rid_recorder import RIdRecorder


def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger("SendMessage")


def log_level_to_python(level: LogLevel) -> int:
    """将 iMooIm 日志级别转换为 Python logging 级别"""
    mapping = {
        LogLevel.TRACE: logging.DEBUG,
        LogLevel.DEBUG: logging.DEBUG,
        LogLevel.INFORMATION: logging.INFO,
        LogLevel.WARNING: logging.WARNING,
        LogLevel.ERROR: logging.ERROR,
        LogLevel.CRITICAL: logging.CRITICAL,
        LogLevel.NONE: logging.NOTSET
    }
    return mapping.get(level, logging.INFO)


def create_device_info() -> DeviceInfo:
    """创建设备信息 - 请修改为您的实际设备信息"""
    device_info = DeviceInfo()
    
    # 请修改为您的实际设备信息
    device_info.baseband_version = "YOUR_BASEBAND_VERSION"
    device_info.build_number = "YOUR_BUILD_NUMBER"
    device_info.bind_number = "YOUR_BIND_NUMBER"
    device_info.chip_id = "YOUR_CHIP_ID"
    device_info.account_id = "YOUR_ACCOUNT_ID"
    device_info.model_number = "YOUR_MODEL_NUMBER"
    device_info.sdk_version = 27
    device_info.sys_name = "YOUR_SYS_NAME"
    device_info.sys_version = "YOUR_SYS_VERSION"
    device_info.eebbk_key = ""
    
    return device_info


def send_text_message(client: ImClient, receiver_id: str, content: str) -> bool:
    """
    发送文本消息
    
    Args:
        client: IM客户端实例
        receiver_id: 接收者ID（好友的account_id或bind_number）
        content: 消息内容
        
    Returns:
        bool: 发送是否成功
    """
    try:
        # 创建消息请求
        message_request = SingleMessageRequestMessage()
        message_request.r_id = RIdRecorder.get_r_id()
        message_request.receiver_id = receiver_id
        message_request.account_id = client._im_account_id
        message_request.regist_id = client._regist_id
        message_request.msg_type = 1  # 文本消息类型
        message_request.content_type = 3  # 内容类型
        message_request.need_response = 0
        message_request.no_sensitivity = 0
        message_request.msg_id = str(uuid.uuid4()).replace('-', '')

        # 构造消息内容
        msg_content = {"content": content}
        message_request.msg = json.dumps(msg_content).encode('utf-8')

        # 发送消息
        if client._tcp_connection:
            client._tcp_connection.send(message_request.encode())
            return True
        else:
            logging.error("TCP连接为空，无法发送消息")
            return False
            
    except Exception as e:
        logging.error(f"发送消息失败: {e}")
        return False


def main():
    """主函数"""
    logger = setup_logging()
    core_logger = logging.getLogger("iMooIm-Core")

    logger.info("=" * 60)
    logger.info("iMooIm Python 客户端 - 发送消息示例")
    logger.info("=" * 60)

    # 好友信息配置
    # 请修改为您要发送消息的好友信息
    friend_config = {
        "name": "好友昵称",  # 仅用于显示
        "receiver_id": "FRIEND_ACCOUNT_ID_OR_BIND_NUMBER",  # 好友的account_id或bind_number
    }

    # 创建设备和应用信息
    device_info = create_device_info()
    app_info = AppInfo()

    # 创建客户端
    client = ImClient(device_info, app_info, 0)

    # 设置事件回调
    def on_log(sender, event: LogEvent):
        python_level = log_level_to_python(event.level)
        core_logger.log(python_level, event.message)

    def on_text_message(sender, event: TextMessageEvent):
        logger.info(f"📨 收到来自好友的消息: {event.content}")
        logger.info(f"   对话ID: {event.dialog_id}")
        logger.info(f"   好友ID: {event.friend_id}")

    # 绑定事件处理器
    client.on_log = on_log
    client.on_receive_text_message = on_text_message

    try:
        # 初始化和登录
        logger.info("🔧 正在初始化客户端...")
        client.init()
        logger.info("✅ 客户端初始化完成")

        logger.info("🔐 开始登录流程...")
        client.login()
        logger.info("✅ 登录成功！")

        # 等待一下确保连接稳定
        time.sleep(2)

        # 发送测试消息
        test_messages = [
            f"Hello! 这是来自Python客户端的测试消息 - {time.strftime('%H:%M:%S')}",
            "😊 支持emoji表情",
            "这是第三条测试消息"
        ]

        logger.info(f"📤 准备向好友 '{friend_config['name']}' 发送 {len(test_messages)} 条消息...")

        for i, message in enumerate(test_messages, 1):
            logger.info(f"📝 发送第 {i} 条消息: {message}")
            
            success = send_text_message(client, friend_config["receiver_id"], message)
            
            if success:
                logger.info(f"✅ 第 {i} 条消息发送成功")
            else:
                logger.error(f"❌ 第 {i} 条消息发送失败")
            
            # 消息间隔
            time.sleep(2)

        # 保持连接一段时间以观察回复
        logger.info("⏰ 保持连接30秒以观察好友回复...")
        time.sleep(30)

    except Exception as e:
        logger.error(f"❌ 发生错误: {e}")
        import traceback
        logger.error("详细错误信息:")
        logger.error(traceback.format_exc())
        
        logger.info("\n💡 发送消息失败的可能原因:")
        logger.info("1. 好友ID (receiver_id) 不正确")
        logger.info("2. 网络连接不稳定")
        logger.info("3. 登录状态异常")
        logger.info("4. 消息格式错误")
        
    finally:
        # 关闭客户端
        logger.info("🔌 正在关闭客户端...")
        client.close()
        logger.info("✅ 客户端已关闭")


if __name__ == "__main__":
    main()
