﻿using iMooIm.Core.Data.Attributes;

namespace iMooIm.Core.Data.Messages;
[CommandId(1)]
public class RegistRequestMessage : BaseMessage
{
    [TagId(11)]
    public String AppKey { get; set; }
    [TagId(21)]
    public String BasebandVersion { get; set; }
    [TagId(22)]
    public String BuildNumber { get; set; }
    [TagId(13)]
    public String DeviceId { get; set; }
    [TagId(17)]
    public String Imei { get; set; }
    [TagId(18)]
    public String Imsi { get; set; }
    [TagId(19)]
    public String Mac { get; set; }
    [TagId(20)]
    public String ModelNumber { get; set; }
    [TagId(12)]
    public String PkgName { get; set; }
    [TagId(10)]
    public int Platform { get; set; }
    [TagId(23)]
    public String Resolution { get; set; }
    [TagId(14)]
    public int SdkVerison { get; set; }
    [TagId(15)]
    public String SysName { get; set; }
    [TagId(16)]
    public String SysVersion { get; set; }
}