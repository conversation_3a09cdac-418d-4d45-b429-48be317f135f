﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.TcpConnection
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;
using iMooIm.Core.Data.Enums;
using iMooIm.Core.Events;
using iMooIm.Core.Message;
using iMooIm.Core.Message.Response;
using iMooIm.Core.Tools;
using System;
using System.Collections.Generic;
using System.Net.Sockets;
using System.Reflection;
using System.Text.Json;
using System.Threading;

#nullable enable
namespace iMooIm.Core;

internal class TcpConnection
{
  private readonly Socket? _socket;
  private byte[] _buffer = new byte[1048576 /*0x100000*/];
  private Dictionary<int, TcpMessage> _messageMap = new Dictionary<int, TcpMessage>();
  internal static readonly byte[] EncryptKey = new byte[15]
  {
    (byte) 1,
    (byte) 1,
    (byte) 1,
    (byte) 1,
    (byte) 1,
    (byte) 1,
    (byte) 1,
    (byte) 1,
    (byte) 1,
    (byte) 2,
    (byte) 3,
    (byte) 1,
    (byte) 1,
    (byte) 1,
    (byte) 1
  };

  internal event EventHandler<LogEvent>? OnLog;

  internal event EventHandler<ReceiveMessageEvent>? OnReceiveSyncMessage;

  internal event EventHandler<ReceiveMessageEvent>? OnReceiveMessage;

  internal event EventHandler<ReceiveMessageEvent>? OnReceiveSyncFinishMessage;

  internal event EventHandler<ReceiveMessageEvent>? OnNeedSync;

  internal TcpConnection()
  {
    this._socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
  }

  internal T GetResponse<T>(int rId) where T : BaseMessage
  {
    while (!this._messageMap.ContainsKey(rId))
    {
      EventHandler<LogEvent> onLog = this.OnLog;
      if (onLog != null)
        onLog((object) this, new LogEvent()
        {
          Level = LogLevel.Warning,
          Message = $"Response not found id:{rId.ToString()} waiting..."
        });
      Thread.Sleep(50);
    }
    this._messageMap[rId].Lock?.WaitOne();
    return (T) this._messageMap[rId].Response;
  }

  internal void Init(string host, int port)
  {
    this._socket?.Connect(host, port);
    Socket socket = this._socket;
    if (socket != null && socket.Connected)
    {
      EventHandler<LogEvent> onLog = this.OnLog;
      if (onLog != null)
        onLog((object) this, new LogEvent()
        {
          Message = $"Connected to {host}:{port.ToString()}",
          Level = LogLevel.Information
        });
      this._socket.BeginReceive(this._buffer, 0, this._buffer.Length, SocketFlags.None, new AsyncCallback(this.ReceiveCallback), (object) this._socket);
    }
    else
    {
      EventHandler<LogEvent> onLog = this.OnLog;
      if (onLog != null)
        onLog((object) this, new LogEvent()
        {
          Level = LogLevel.Critical,
          Message = $"Failed to connect to {host}:{port.ToString()}"
        });
      throw new Exception($"Failed to connect to {host}:{port.ToString()}");
    }
  }

  private void ReceiveCallback(IAsyncResult result)
  {
    Socket asyncState = (Socket) result.AsyncState;
    object obj = MessageDecoder.Decode(this._buffer);
    switch (obj)
    {
      case BaseMessage baseMessage:
        if (this._messageMap.ContainsKey(baseMessage.RId))
        {
          EventHandler<LogEvent> onLog = this.OnLog;
          if (onLog != null)
            onLog((object) this, new LogEvent()
            {
              Level = LogLevel.Trace,
              Message = "Received message: " + JsonSerializer.Serialize<object>(obj)
            });
          this._messageMap[baseMessage.RId].Response = obj;
          ManualResetEvent manualResetEvent = this._messageMap[baseMessage.RId].Lock;
          if (manualResetEvent != null)
          {
            manualResetEvent.Set();
            break;
          }
          break;
        }
        EventHandler<LogEvent> onLog1 = this.OnLog;
        if (onLog1 != null)
          onLog1((object) this, new LogEvent()
          {
            Level = LogLevel.Warning,
            Message = $"Received message: {baseMessage.RId.ToString()} but not found in message map. Raw message: {JsonSerializer.Serialize<object>(obj)}"
          });
        break;
      case BaseMessageWithOutRid _:
        EventHandler<LogEvent> onLog2 = this.OnLog;
        if (onLog2 != null)
          onLog2((object) this, new LogEvent()
          {
            Level = LogLevel.Trace,
            Message = "Received message: " + JsonSerializer.Serialize<object>(obj)
          });
        if (obj is SyncResponseMessage syncResponseMessage)
        {
          EventHandler<ReceiveMessageEvent> receiveSyncMessage = this.OnReceiveSyncMessage;
          if (receiveSyncMessage != null)
            receiveSyncMessage((object) this, new ReceiveMessageEvent()
            {
              Message = (object) syncResponseMessage
            });
        }
        if (obj is SyncInformResponseMessage informResponseMessage && informResponseMessage.SyncKey != SyncKeyManager.ServerSyncKey)
        {
          SyncKeyManager.ServerSyncKey = informResponseMessage.SyncKey;
          EventHandler<ReceiveMessageEvent> onNeedSync = this.OnNeedSync;
          if (onNeedSync != null)
            onNeedSync((object) this, new ReceiveMessageEvent()
            {
              Message = (object) informResponseMessage
            });
        }
        if (obj is SyncFinishResponseMessage finishResponseMessage)
        {
          EventHandler<ReceiveMessageEvent> syncFinishMessage = this.OnReceiveSyncFinishMessage;
          if (syncFinishMessage != null)
            syncFinishMessage((object) this, new ReceiveMessageEvent()
            {
              Message = (object) finishResponseMessage
            });
        }
        if (obj is PushResponseMessage pushResponseMessage)
        {
          EventHandler<ReceiveMessageEvent> onReceiveMessage = this.OnReceiveMessage;
          if (onReceiveMessage != null)
            onReceiveMessage((object) this, new ReceiveMessageEvent()
            {
              Message = (object) pushResponseMessage
            });
        }
        break;
    }
    asyncState.EndReceive(result);
    result.AsyncWaitHandle.Close();
    Array.Clear((Array) this._buffer);
    asyncState.BeginReceive(this._buffer, 0, this._buffer.Length, SocketFlags.None, new AsyncCallback(this.ReceiveCallback), (object) asyncState);
  }

  internal void SendWithoutCheck(byte[] data) => this._socket?.Send(data);

  internal void Send(byte[] data)
  {
    IEnumerable<Attribute> attributes = MessageDecoder.Decode(data) is BaseMessage baseMessage ? baseMessage.GetType().GetCustomAttributes() : throw new ArgumentException("Invalid message");
    int num = 0;
    foreach (Attribute attribute in attributes)
    {
      if (attribute is CommandId commandId)
        num = commandId.Id;
    }
    this._messageMap.Add(baseMessage.RId, new TcpMessage()
    {
      Lock = new ManualResetEvent(false),
      Response = (object) null
    });
    bool flag = num != 7 && num != 22 && num != 33;
    byte[] numArray = data;
    if (flag)
      numArray = new EncryptWrapper()
      {
        Payload = EncryptUtil.Encrypt(numArray, TcpConnection.EncryptKey)
      }.Encode();
    EventHandler<LogEvent> onLog = this.OnLog;
    if (onLog != null)
      onLog((object) this, new LogEvent()
      {
        Level = LogLevel.Debug,
        Message = $"Sending message: {baseMessage.RId.ToString()} command: {num.ToString()} isEncrypt: {flag.ToString()}"
      });
    this._socket?.Send(numArray);
  }
}
