"""
单条消息请求消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(40)
class SingleMessageRequestMessage(BaseMessage):
    """
    单条消息请求消息类
    
    用于发送单条消息
    """
    
    def __init__(self):
        super().__init__()
        self._receiver_id: int = 0
        self._account_id: int = 0
        self._regist_id: int = 0
        self._msg_type: int = 0
        self._msg: bytes = b''
        self._msg_id: str = ""
        self._content_type: int = 0
        self._need_response: int = 0
        self._no_sensitivity: int = 0

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'need_response': 2,
            'receiver_id': 10,
            'account_id': 11,
            'regist_id': 12,
            'msg_type': 13,
            'msg': 14,
            'msg_id': 15,
            'content_type': 16,
            'no_sensitivity': 18
        }

    @property
    def receiver_id(self) -> int:
        """接收者ID"""
        return self._receiver_id

    @receiver_id.setter
    def receiver_id(self, value: int):
        self._receiver_id = value

    @property
    def account_id(self) -> int:
        """账户ID"""
        return self._account_id

    @account_id.setter
    def account_id(self, value: int):
        self._account_id = value

    @property
    def regist_id(self) -> int:
        """注册ID"""
        return self._regist_id

    @regist_id.setter
    def regist_id(self, value: int):
        self._regist_id = value

    @property
    def msg_type(self) -> int:
        """消息类型"""
        return self._msg_type

    @msg_type.setter
    def msg_type(self, value: int):
        self._msg_type = value

    @property
    def msg(self) -> bytes:
        """消息内容"""
        return self._msg

    @msg.setter
    def msg(self, value: bytes):
        self._msg = value

    @property
    def msg_id(self) -> str:
        """消息ID"""
        return self._msg_id

    @msg_id.setter
    def msg_id(self, value: str):
        self._msg_id = value

    @property
    def content_type(self) -> int:
        """内容类型"""
        return self._content_type

    @content_type.setter
    def content_type(self, value: int):
        self._content_type = value

    @property
    def need_response(self) -> int:
        """是否需要响应"""
        return self._need_response

    @need_response.setter
    def need_response(self, value: int):
        self._need_response = value

    @property
    def no_sensitivity(self) -> int:
        """无敏感性"""
        return self._no_sensitivity

    @no_sensitivity.setter
    def no_sensitivity(self, value: int):
        self._no_sensitivity = value
