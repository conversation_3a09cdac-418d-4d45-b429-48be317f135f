"""
账户请求消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(5)
class AccountRequestMessage(BaseMessage):
    """
    账户请求消息类

    用于设置账户状态
    """

    def __init__(self):
        super().__init__()
        self._app_key: str = ""
        self._business_id: str = ""
        self._business_token: str = ""
        self._business_type: int = 0
        self._low_power: int = 0
        self._regist_id: int = 0
        self._status: int = 0

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'regist_id': 10,
            'business_type': 11,
            'business_id': 12,
            'business_token': 13,
            'status': 14,
            'app_key': 15,
            'low_power': 16
        }

    @property
    def app_key(self) -> str:
        """应用密钥"""
        return self._app_key

    @app_key.setter
    def app_key(self, value: str):
        self._app_key = value

    @property
    def business_id(self) -> str:
        """业务ID"""
        return self._business_id

    @business_id.setter
    def business_id(self, value: str):
        self._business_id = value

    @property
    def business_token(self) -> str:
        """业务令牌"""
        return self._business_token

    @business_token.setter
    def business_token(self, value: str):
        self._business_token = value

    @property
    def business_type(self) -> int:
        """业务类型"""
        return self._business_type

    @business_type.setter
    def business_type(self, value: int):
        self._business_type = value

    @property
    def low_power(self) -> int:
        """低功耗模式"""
        return self._low_power

    @low_power.setter
    def low_power(self, value: int):
        self._low_power = value

    @property
    def regist_id(self) -> int:
        """注册ID"""
        return self._regist_id

    @regist_id.setter
    def regist_id(self, value: int):
        self._regist_id = value

    @property
    def status(self) -> int:
        """状态"""
        return self._status

    @status.setter
    def status(self, value: int):
        self._status = value
