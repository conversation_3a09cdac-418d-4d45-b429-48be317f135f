﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Data.Attributes.TagId
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using System;

#nullable disable
namespace iMooIm.Core.Data.Attributes;

[AttributeUsage(AttributeTargets.Property)]
internal class TagId(int id) : Attribute
{
  public int Id { get; set; } = id;
}
