﻿using System.ComponentModel;
using System.Reflection;
using System.Text;
using System.Text.Json;
using iMooIm.Core.Data.Attributes;
using iMooIm.Core.Data.Tlv;
using iMooIm.Core.Tools;

namespace iMooIm.Core.Data;

public static class MessageDecoder
{
    public static bool InitState = false;
    public static Dictionary<int, Type> CommandMap = new();

    public static void Init()
    {
        static IEnumerable<Type> GetClassesInNamespace(Assembly assembly, string @namespace)
        {
            return assembly.GetTypes()
                .Where(t => String.Equals(t.Namespace, @namespace, StringComparison.Ordinal)
                            || t.Namespace?.StartsWith(@namespace + ".", StringComparison.Ordinal) == true)
                .Where(t => t.IsClass && !t.IsAbstract); // 获取非抽象类
        }

        string namespaceName = "iMooIm.Core.Data.Messages";
        Assembly assembly = Assembly.GetExecutingAssembly();
        var classes = GetClassesInNamespace(assembly, namespaceName);
        foreach (var @class in classes)
        {
            var attrs = @class.GetCustomAttributes();
            foreach (var attr in attrs)
            {
                var command = -1;
                if (attr is CommandId commandId)
                {
                    command = commandId.Id;
                }
                if (command == -1)
                {
                    continue;
                }
                CommandMap.Add(command, @class);
            }
        }
        InitState = true;
    }

    public static object Decode(byte[] data)
    {
        if (!InitState)
        {
            throw new InvalidOperationException("MessageDecoder not initialized");
        }
        var decodeResult = TLVDecoder.Decode(data);
        if (decodeResult.TagValue == 32)
        {
            Console.WriteLine(JsonSerializer.Serialize(decodeResult));
        }
        var result = Activator.CreateInstance(CommandMap[decodeResult.TagValue])!;;
        var attrs = result.GetType().GetCustomAttributes();
        int command = 0;
        foreach (var attr in attrs)
        {
            if (attr is CommandId commandId)
            {
                command = commandId.Id;
            }
        }

        if (command != decodeResult.TagValue)
        {
            throw new ArgumentException("CommandId does not match");
        }

        var properties = result.GetType().GetProperties();
        foreach (var property in properties)
        {
            var tagId = -1;
            if (property.GetCustomAttribute<TagId>() is { } tag)
            {
                tagId = tag.Id;
            }

            if (tagId == -1)
            {
                throw new ArgumentException("TagId not found");
            }

            object? tlvData = (decodeResult.Value as List<TLVDecodeResult>)?.FirstOrDefault(x => x.TagValue == tagId)
                ?.Value;
            var type = property.PropertyType;
            if (type == typeof(byte[]))
            {
                property.SetValue(result, tlvData as byte[] ?? []);
                continue;
            }

            if (type == typeof(string))
            {
                property.SetValue(result, Encoding.UTF8.GetString(tlvData as byte[] ?? []));
                continue;
            }

            if (type == typeof(int))
            {
                var intArr = tlvData as byte[] ?? [];
                property.SetValue(result, DecodeToInt(intArr));
                continue;
            }

            if (type == typeof(long))
            {
                var intArr = tlvData as byte[] ?? [];
                property.SetValue(result, DecodeToLong(intArr));
            }
        }

        return result;
    }

    private static long DecodeToLong(byte[] arr)
    {
        if (BitConverter.IsLittleEndian)
        {
            Array.Reverse(arr);
        }

        switch (arr.Length)
        {
            case 0:
            {
                return 0;
            }
            case 1:
            {
                return arr[0];
            }
            case 2:
            {
                return BitConverter.ToInt16(arr, 0);
            }
            case 4:
            {
                return BitConverter.ToInt32(arr, 0);
            }
            case 8:
            {
                return BitConverter.ToInt64(arr, 0);
            }
            default:
            {
                throw new ArgumentException("Invalid array length");
            }
        }
    }

    private static int DecodeToInt(byte[] arr)
    {
        if (BitConverter.IsLittleEndian)
        {
            Array.Reverse(arr);
        }

        switch (arr.Length)
        {
            case 0:
            {
                return 0;
            }
            case 1:
            {
                return arr[0];
            }
            case 2:
            {
                return BitConverter.ToInt16(arr, 0);
            }
            case 4:
            {
                return BitConverter.ToInt32(arr, 0);
            }
            default:
            {
                throw new ArgumentException("Invalid array length:"+arr.Length);
            }
        }
    }
}