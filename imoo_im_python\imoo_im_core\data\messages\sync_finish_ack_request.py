"""
同步完成确认请求消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(15)
class SyncFinishAckRequest(BaseMessage):
    """
    同步完成确认请求消息类
    
    用于确认同步完成
    """
    
    def __init__(self):
        super().__init__()
        self._regist_id: int = 0
        self._im_account_id: int = 0
        self._sync_key: int = 0

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'im_account_id': 11,
            'regist_id': 12,
            'sync_key': 13
        }

    @property
    def regist_id(self) -> int:
        """注册ID"""
        return self._regist_id

    @regist_id.setter
    def regist_id(self, value: int):
        self._regist_id = value

    @property
    def im_account_id(self) -> int:
        """IM账户ID"""
        return self._im_account_id

    @im_account_id.setter
    def im_account_id(self, value: int):
        self._im_account_id = value

    @property
    def sync_key(self) -> int:
        """同步密钥"""
        return self._sync_key

    @sync_key.setter
    def sync_key(self, value: int):
        self._sync_key = value
