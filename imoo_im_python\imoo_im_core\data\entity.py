"""
实体类 - 用于消息的编码和数据存储
"""

from typing import Dict, Any, Union
import io
from .tlv.tlv_encoder import TlvEncoder
from ..tools.int_encoder import encode_to_bytes


class Entity:
    """
    实体类，用于存储消息数据并进行编码
    """
    
    def __init__(self):
        self.data: Dict[int, Any] = {}
        self.command_id: int = 0
    
    def encode(self) -> bytes:
        """
        将实体编码为字节数组
        
        Returns:
            编码后的字节数组
        """
        main_stream = io.BytesIO()
        sub_stream = io.BytesIO()
        
        # 编码所有数据项
        for tag, value in self.data.items():
            data_bytes = self._encode_value(value)
            tlv_bytes = TlvEncoder.encode_tlv(tag, len(data_bytes), data_bytes)
            sub_stream.write(tlv_bytes)
        
        # 编码嵌套 TLV
        sub_data = sub_stream.getvalue()
        nested_tlv = TlvEncoder.encode_nested_tlv(32, self.command_id, len(sub_data), sub_data)
        main_stream.write(nested_tlv)
        
        return main_stream.getvalue()
    
    def _encode_value(self, value: Any) -> bytes:
        """
        编码单个值
        
        Args:
            value: 要编码的值
            
        Returns:
            编码后的字节数组
        """
        if isinstance(value, int):
            return encode_to_bytes(value)
        elif isinstance(value, (int, int)):
            return encode_to_bytes(value)
        elif isinstance(value, bytes):
            return value
        elif isinstance(value, str):
            return value.encode('utf-8')
        else:
            # 默认转换为字符串再编码
            return str(value).encode('utf-8')
