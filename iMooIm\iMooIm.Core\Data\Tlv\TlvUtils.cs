﻿namespace iMooIm.Core.Data.Tlv;

public class TLVUtils {
    /* renamed from: a */
    public static long m17981a(byte[] p0) {
        int len = p0.Length;
        long result;

        if (len == 1) {
            result = (p0[0] & 0xFFL);
        } else if (len == 2) {
            result = ((p0[0] & 0xFFL) << 8) | (p0[1] & 0xFFL);
        } else if (len == 4) {
            result = ((p0[0] & 0xFFL) << 24) |
                     ((p0[1] & 0xFFL) << 16) |
                     ((p0[2] & 0xFFL) << 8) |
                     (p0[3] & 0xFFL);
        } else if (len == 8) {
            result = ((p0[0] & 0xFFL) << 56) |
                     ((p0[1] & 0xFFL) << 48) |
                     ((p0[2] & 0xFFL) << 40) |
                     ((p0[3] & 0xFFL) << 32) |
                     ((p0[4] & 0xFFL) << 24) |
                     ((p0[5] & 0xFFL) << 16) |
                     ((p0[6] & 0xFFL) << 8) |
                     (p0[7] & 0xFFL);
        } else {
            throw new ArgumentException("the length of byte array is incorrect.");
        }

        return result;
    }

    /* renamed from: a */
    public static byte[] m17982a(long j) {
        if (j == (byte) j) {
            return m17983a(j, 1);
        }
        if (j == (short) j) {
            return m17983a(j, 2);
        }
        if (j == (int) j) {
            return m17983a(j, 4);
        }
        return m17983a(j, 8);
    }

    /* renamed from: a */
    private static byte[] m17983a(long j, int i) {
        byte[] bArr = new byte[i];
        for (int i2 = 0; i2 < i; i2++) {
            bArr[i2] = (byte) (j >>> (((i - i2) - 1) * 8));
        }
        return bArr;
    }
}