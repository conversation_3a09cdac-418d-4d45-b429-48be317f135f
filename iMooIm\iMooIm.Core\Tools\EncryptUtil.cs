﻿using System.Security.Cryptography;
using System.Text;
using Org.BouncyCastle.Asn1.X509;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Encodings;
using Org.BouncyCastle.Crypto.Engines;
using Org.BouncyCastle.OpenSsl;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.X509;

namespace iMooIm.Core.Tools;

public static class EncryptUtil
{
    public static byte[] ConvertPEMToDER(string pemKey)
    {
        string header = "-----BEGIN PUBLIC KEY-----";
        string footer = "-----END PUBLIC KEY-----";
        int start = pemKey.IndexOf(header, StringComparison.Ordinal) + header.Length;
        int end = pemKey.LastIndexOf(footer, StringComparison.Ordinal);
        if (start == -1 || end == -1)
            throw new ArgumentException("Invalid PEM format");
        string base64Key = pemKey.Substring(start, end - start).Replace("\n", "").Replace("\r", "");
        return Convert.FromBase64String(base64Key);
    }
    public static byte[] EncryptKey(byte[] dataToEncrypt, string publicKeyBytes)
    {
        SubjectPublicKeyInfo publicKeyInfo = SubjectPublicKeyInfo.GetInstance(ConvertPEMToDER(publicKeyBytes));
        AsymmetricKeyParameter publicKeyParam = PublicKeyFactory.CreateKey(publicKeyInfo);
        IBufferedCipher cipher = CipherUtilities.GetCipher("RSA/ECB/PKCS1Padding");
        cipher.Init(true, publicKeyParam);
        return cipher.DoFinal(dataToEncrypt);
    }
    public static byte[] Encrypt(byte[] srcData, byte[] secretKey)
    {
        int length = srcData.Length;
        int length2 = secretKey.Length;
        if (length == 0 || length2 == 0) {
            return srcData;
        }
        var byteArrayOutputStream = new MemoryStream();
        int i = 0;
        for (int i2 = 0; i2 < length; i2++) {
            if (i >= length2) {
                i = 0;
            }
            byteArrayOutputStream.WriteByte((byte)(srcData[i2] ^ secretKey[i]));
            i++;
        }
        return byteArrayOutputStream.ToArray();
    }
}