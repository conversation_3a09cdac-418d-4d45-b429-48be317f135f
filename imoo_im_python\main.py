"""
iMooIm Python 客户端示例程序

这个示例展示了如何使用 Python 版本的 iMooIm 客户端进行连接和登录。
功能与原始 C# 版本完全一致。
"""

import logging
import sys
from imoo_im_core.im_client import ImClient
from imoo_im_core.data.account.device_info import DeviceInfo
from imoo_im_core.data.account.app_info import AppInfo
from imoo_im_core.events.log_event import LogEvent
from imoo_im_core.events.text_message_event import TextMessageEvent
from imoo_im_core.events.save_sync_key_event import SaveSyncKeyEvent
from imoo_im_core.data.enums.log_level import LogLevel


def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger("iMooIm-Python")


def log_level_to_python(level: LogLevel) -> int:
    """将 iMooIm 日志级别转换为 Python logging 级别"""
    mapping = {
        LogLevel.TRACE: logging.DEBUG,
        LogLevel.DEBUG: logging.DEBUG,
        LogLevel.INFORMATION: logging.INFO,
        LogLevel.WARNING: logging.WARNING,
        LogLevel.ERROR: logging.ERROR,
        LogLevel.CRITICAL: logging.CRITICAL,
        LogLevel.NONE: logging.NOTSET
    }
    return mapping.get(level, logging.INFO)


def create_device_info() -> DeviceInfo:
    """
    创建设备信息

    请根据您的实际设备信息修改以下参数：
    - baseband_version: 基带版本
    - build_number: 构建号
    - bind_number: 绑定号（设备唯一标识）
    - chip_id: 芯片ID
    - account_id: 账户ID
    - model_number: 型号
    - sys_name: 系统名称
    - sys_version: 系统版本
    """
    device_info = DeviceInfo()

    # 请修改为您的实际设备信息
    device_info.baseband_version = "YOUR_BASEBAND_VERSION"
    device_info.build_number = "YOUR_BUILD_NUMBER"
    device_info.bind_number = "YOUR_BIND_NUMBER"
    device_info.chip_id = "YOUR_CHIP_ID"
    device_info.account_id = "YOUR_ACCOUNT_ID"
    device_info.model_number = "YOUR_MODEL_NUMBER"
    device_info.sdk_version = 27
    device_info.sys_name = "YOUR_SYS_NAME"
    device_info.sys_version = "YOUR_SYS_VERSION"
    device_info.eebbk_key = ""

    return device_info


def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()
    core_logger = logging.getLogger("iMooIm-Core")

    logger.info("=" * 60)
    logger.info("iMooIm Python 客户端示例")
    logger.info("=" * 60)
    logger.info("⚠️  请先在 create_device_info() 函数中填入您的实际设备信息")

    # 创建设备信息
    device_info = create_device_info()

    # 创建应用信息
    app_info = AppInfo()

    # 创建客户端
    client = ImClient(device_info, app_info, 0)

    # 设置日志回调
    def on_log(sender, event: LogEvent):
        python_level = log_level_to_python(event.level)
        core_logger.log(python_level, event.message)

    def on_text_message(sender, event: TextMessageEvent):
        logger.info(f"收到文本消息: {event.content} (对话ID: {event.dialog_id}, 好友ID: {event.friend_id})")

    def on_save_sync_key(sender, event: SaveSyncKeyEvent):
        logger.info(f"需要保存同步键: {event.sync_key}")

    client.on_log = on_log
    client.on_receive_text_message = on_text_message
    client.on_save_sync_key_event = on_save_sync_key

    try:
        # 初始化客户端
        logger.info("🔧 正在初始化客户端...")
        client.init()
        logger.info("✅ 客户端初始化完成")

        # 执行登录
        logger.info("🔐 开始登录流程...")
        client.login()
        logger.info("✅ 登录成功！")

        # 保持连接一段时间以便观察
        import time
        logger.info("⏰ 保持连接30秒以观察消息...")
        time.sleep(30)

    except Exception as e:
        logger.error(f"❌ 发生错误: {e}")
        import traceback
        logger.error("详细错误信息:")
        logger.error(traceback.format_exc())

        # 提供一些常见问题的解决建议
        logger.info("\n💡 常见问题解决建议:")
        logger.info("1. 检查设备信息是否正确填写")
        logger.info("2. 确认网络连接正常")
        logger.info("3. 验证服务器地址和端口")
        logger.info("4. 检查防火墙设置")

    finally:
        # 关闭客户端
        logger.info("🔌 正在关闭客户端...")
        client.close()
        logger.info("✅ 客户端已关闭")


if __name__ == "__main__":
    main()
