"""
命令 ID 装饰器 - 用于标识消息类型的命令 ID
"""

from typing import Type, TypeVar

T = TypeVar('T')


def command_id(id_value: int):
    """
    命令 ID 装饰器，用于标识消息类型的命令 ID
    
    Args:
        id_value: 命令 ID 值
    """
    def decorator(cls: Type[T]) -> Type[T]:
        cls._command_id = id_value
        return cls
    return decorator


def get_command_id(cls: Type) -> int:
    """
    获取类的命令 ID
    
    Args:
        cls: 要获取命令 ID 的类
        
    Returns:
        命令 ID 值，如果没有设置则返回 -1
    """
    return getattr(cls, '_command_id', -1)
