"""
TCP 连接模块 - 处理与服务器的 TCP 连接和消息收发
"""

import socket
import threading
import time
import uuid
import json
from typing import Dict, Optional, TypeVar, Type, Callable, Any
from .data.base_message import BaseMessage, BaseMessageWithoutRid
from .data.message_decoder import MessageDecoder
from .data.messages.encrypt_wrapper import EncryptWrapper
from .data.attributes.command_id import get_command_id
from .events.log_event import LogEvent
from .events.receive_message_event import ReceiveMessageEvent
from .tools.encrypt_util import EncryptUtil
from .tools.sync_key_manager import SyncKeyManager

T = TypeVar('T', bound=BaseMessage)


class TcpMessage:
    """TCP 消息包装类，用于等待响应"""
    
    def __init__(self):
        self.lock = threading.Event()
        self.response: Optional[Any] = None


class TcpConnection:
    """TCP 连接类，负责与服务器的通信"""
    
    # 静态加密密钥 - 必须与C#版本完全一致
    encrypt_key = bytes([1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 3, 1, 1, 1, 1])
    
    def __init__(self):
        self._socket: Optional[socket.socket] = None
        self._buffer = bytearray(1024)
        self._message_map: Dict[int, TcpMessage] = {}
        self._running = False
        self._receive_thread: Optional[threading.Thread] = None
        
        # 事件回调
        self.on_log: Optional[Callable[[Any, LogEvent], None]] = None
        self.on_receive_message: Optional[Callable[[Any, ReceiveMessageEvent], None]] = None
        self.on_receive_sync_message: Optional[Callable[[Any, ReceiveMessageEvent], None]] = None
        self.on_receive_sync_finish_message: Optional[Callable[[Any, ReceiveMessageEvent], None]] = None
        self.on_need_sync: Optional[Callable[[Any, ReceiveMessageEvent], None]] = None
    
    def init(self, host: str, port: int):
        """
        初始化 TCP 连接
        
        Args:
            host: 服务器主机名
            port: 服务器端口
        """
        try:
            self._socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self._socket.connect((host, port))
            
            if self._socket:
                self._log_info(f"已连接到 {host}:{port}")
                self._running = True
                
                # 启动接收线程
                self._receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
                self._receive_thread.start()
            else:
                raise Exception(f"连接到 {host}:{port} 失败")

        except Exception as e:
            self._log_critical(f"连接到 {host}:{port} 失败: {e}")
            raise
    
    def get_response(self, r_id: int, message_type: Type[T], timeout: float = 30.0) -> T:
        """
        获取指定请求 ID 的响应
        
        Args:
            r_id: 请求 ID
            message_type: 期望的消息类型
            timeout: 超时时间（秒）
            
        Returns:
            响应消息对象
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if r_id in self._message_map:
                tcp_message = self._message_map[r_id]
                if tcp_message.lock.wait(timeout=0.05):  # 等待 50ms
                    response = tcp_message.response
                    del self._message_map[r_id]  # 清理
                    return response
            else:
                self._log_warning(f"未找到响应 id:{r_id} 等待中...")
                time.sleep(0.05)

        raise TimeoutError(f"等待响应 {r_id} 超时")
    
    def send(self, data: bytes):
        """
        发送数据
        
        Args:
            data: 要发送的数据
        """
        if not self._socket:
            raise RuntimeError("套接字未连接")
        
        # 解码消息以获取信息
        message = MessageDecoder.decode(data)
        if not isinstance(message, BaseMessage):
            raise ValueError("无效消息")
        
        # 获取命令 ID
        command = get_command_id(message.__class__)
        
        # 注册消息等待
        self._message_map[message.r_id] = TcpMessage()
        
        # 判断是否需要加密
        is_encrypt = command not in [7, 22, 33]
        
        self._log_debug(f"发送消息: {message.r_id} 命令: {command} 是否加密: {is_encrypt}")
        
        send_data = data
        if is_encrypt:
            # 加密数据
            encrypt_wrapper = EncryptWrapper()
            encrypt_wrapper.payload = EncryptUtil.encrypt(data, self.encrypt_key)
            send_data = encrypt_wrapper.encode()
        
        # 发送数据
        self._socket.send(send_data)

    def send_without_check(self, data: bytes):
        """
        发送数据（不检查响应）

        Args:
            data: 要发送的数据
        """
        if not self._socket:
            raise RuntimeError("套接字未连接")

        # 直接发送数据
        self._socket.send(data)

    def close(self):
        """关闭连接"""
        self._running = False
        if self._socket:
            self._socket.close()
            self._socket = None
        if self._receive_thread:
            self._receive_thread.join(timeout=1.0)
    
    def _receive_loop(self):
        """接收数据循环"""
        while self._running and self._socket:
            try:
                # 接收数据
                data = self._socket.recv(1024)
                if not data:
                    break

                self._log_debug(f"接收到原始数据: {len(data)} 字节, 十六进制: {data.hex()}")

                # 解码消息
                try:
                    message = MessageDecoder.decode(data)
                    if message:
                        self._log_debug(f"解码成功: {type(message).__name__}")
                        if isinstance(message, BaseMessage):
                            self._handle_received_message(message, data)
                        elif isinstance(message, BaseMessageWithoutRid):
                            self._handle_received_message_without_rid(message, data)
                    else:
                        self._log_warning(f"消息解码失败，原始数据: {data.hex()}")
                except Exception as decode_error:
                    self._log_error(f"消息解码异常: {decode_error}, 原始数据: {data.hex()}")
                    import traceback
                    self._log_error(traceback.format_exc())

            except Exception as e:
                if self._running:
                    self._log_error(f"接收循环中出错: {e}")
                break
    
    def _handle_received_message(self, message: BaseMessage, raw_data: bytes):
        """
        处理接收到的消息
        
        Args:
            message: 解码后的消息对象
            raw_data: 原始数据
        """
        r_id = message.r_id
        
        if r_id in self._message_map:
            self._log_trace(f"收到消息: {json.dumps(self._message_to_dict(message))}")

            tcp_message = self._message_map[r_id]
            tcp_message.response = message
            tcp_message.lock.set()
        else:
            self._log_warning(f"收到消息: {r_id} 但在消息映射中未找到")
        
        # 触发接收消息事件
        if self.on_receive_message:
            self.on_receive_message(self, ReceiveMessageEvent(message=message))

    def _handle_received_message_without_rid(self, message: BaseMessageWithoutRid, raw_data: bytes):
        """
        处理接收到的无RID消息

        Args:
            message: 解码后的消息对象
            raw_data: 原始数据
        """
        self._log_trace(f"收到消息: {json.dumps(self._message_to_dict(message))}")

        # 导入消息类型
        from .data.messages.sync_response_message import SyncResponseMessage
        from .data.messages.sync_inform_response_message import SyncInformResponseMessage
        from .data.messages.sync_finish_response_message import SyncFinishResponseMessage
        from .data.messages.push_response_message import PushResponseMessage

        # 处理同步响应消息
        if isinstance(message, SyncResponseMessage):
            if self.on_receive_sync_message:
                self.on_receive_sync_message(self, ReceiveMessageEvent(message=message))

        # 处理同步通知消息
        if isinstance(message, SyncInformResponseMessage):
            if message.sync_key != SyncKeyManager.server_sync_key:
                SyncKeyManager.server_sync_key = message.sync_key
                if self.on_need_sync:
                    self.on_need_sync(self, ReceiveMessageEvent(message=message))

        # 处理同步完成消息
        elif isinstance(message, SyncFinishResponseMessage):
            if self.on_receive_sync_finish_message:
                self.on_receive_sync_finish_message(self, ReceiveMessageEvent(message=message))

        # 处理推送消息
        elif isinstance(message, PushResponseMessage):
            if self.on_receive_message:
                self.on_receive_message(self, ReceiveMessageEvent(message=message))
    
    def _message_to_dict(self, message: Any) -> Dict[str, Any]:
        """
        将消息对象转换为字典（用于日志）
        
        Args:
            message: 消息对象
            
        Returns:
            字典表示
        """
        result = {"type": message.__class__.__name__}
        
        # 获取所有属性
        for attr_name in dir(message):
            if not attr_name.startswith('_') and not callable(getattr(message, attr_name)):
                try:
                    value = getattr(message, attr_name)
                    if isinstance(value, bytes):
                        result[attr_name] = value.hex()
                    else:
                        result[attr_name] = value
                except:
                    pass
        
        return result
    
    def _log_trace(self, message: str):
        """记录跟踪日志"""
        if self.on_log:
            from .data.enums.log_level import LogLevel
            self.on_log(self, LogEvent(level=LogLevel.TRACE, message=message))
    
    def _log_debug(self, message: str):
        """记录调试日志"""
        if self.on_log:
            from .data.enums.log_level import LogLevel
            self.on_log(self, LogEvent(level=LogLevel.DEBUG, message=message))
    
    def _log_info(self, message: str):
        """记录信息日志"""
        if self.on_log:
            from .data.enums.log_level import LogLevel
            self.on_log(self, LogEvent(level=LogLevel.INFORMATION, message=message))
    
    def _log_warning(self, message: str):
        """记录警告日志"""
        if self.on_log:
            from .data.enums.log_level import LogLevel
            self.on_log(self, LogEvent(level=LogLevel.WARNING, message=message))
    
    def _log_error(self, message: str):
        """记录错误日志"""
        if self.on_log:
            from .data.enums.log_level import LogLevel
            self.on_log(self, LogEvent(level=LogLevel.ERROR, message=message))
    
    def _log_critical(self, message: str):
        """记录严重错误日志"""
        if self.on_log:
            from .data.enums.log_level import LogLevel
            self.on_log(self, LogEvent(level=LogLevel.CRITICAL, message=message))
