"""
加密设置请求消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(33)
class EncryptSetRequestMessage(BaseMessage):
    """加密设置请求消息，用于设置通信加密"""

    def __init__(self):
        super().__init__()
        self._encrypt_key: bytes = b''
        self._encrypt_type: int = 0

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'encrypt_type': 10,
            'encrypt_key': 11
        }

    @property
    def encrypt_key(self) -> bytes:
        """加密密钥"""
        return self._encrypt_key

    @encrypt_key.setter
    def encrypt_key(self, value: bytes):
        self._encrypt_key = value

    @property
    def encrypt_type(self) -> int:
        """加密类型"""
        return self._encrypt_type

    @encrypt_type.setter
    def encrypt_type(self, value: int):
        self._encrypt_type = value
