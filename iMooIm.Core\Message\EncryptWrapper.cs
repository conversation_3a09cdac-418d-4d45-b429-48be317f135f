﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Message.EncryptWrapper
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;
using System;

#nullable enable
namespace iMooIm.Core.Message;

[CommandId(32 /*0x20*/)]
internal class EncryptWrapper : BaseMessageWithOutRid
{
  [TagId(10)]
  public byte[] Payload { get; set; } = Array.Empty<byte>();
}
