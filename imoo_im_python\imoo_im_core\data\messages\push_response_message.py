"""
推送响应消息
"""

from ..base_message import BaseMessageWithoutRid
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(35)
class PushResponseMessage(BaseMessageWithoutRid):
    """
    推送响应消息类

    服务器推送的消息
    """

    def __init__(self):
        super().__init__()
        self._content_type: int = 0
        self._create_time: int = 0
        self._dialog_id: int = 0
        self._im_account_id: int = 0
        self._msg: bytes = b""
        self._msg_id: str = ""
        self._msg_type: int = 0
        self._regist_id: int = 0
        self._sync_key: int = 0

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'dialog_id': 10,
            'im_account_id': 11,
            'regist_id': 12,
            'msg_type': 13,
            'msg': 14,
            'sync_key': 15,
            'msg_id': 16,
            'create_time': 17,
            'content_type': 18
        }

    @property
    def content_type(self) -> int:
        """内容类型"""
        return self._content_type

    @content_type.setter
    def content_type(self, value: int):
        self._content_type = value

    @property
    def create_time(self) -> int:
        """创建时间"""
        return self._create_time

    @create_time.setter
    def create_time(self, value: int):
        self._create_time = value

    @property
    def dialog_id(self) -> int:
        """对话ID"""
        return self._dialog_id

    @dialog_id.setter
    def dialog_id(self, value: int):
        self._dialog_id = value

    @property
    def im_account_id(self) -> int:
        """IM账户ID"""
        return self._im_account_id

    @im_account_id.setter
    def im_account_id(self, value: int):
        self._im_account_id = value

    @property
    def msg(self) -> bytes:
        """消息内容"""
        return self._msg

    @msg.setter
    def msg(self, value: bytes):
        self._msg = value

    @property
    def msg_id(self) -> str:
        """消息ID"""
        return self._msg_id

    @msg_id.setter
    def msg_id(self, value: str):
        self._msg_id = value

    @property
    def msg_type(self) -> int:
        """消息类型"""
        return self._msg_type

    @msg_type.setter
    def msg_type(self, value: int):
        self._msg_type = value

    @property
    def regist_id(self) -> int:
        """注册ID"""
        return self._regist_id

    @regist_id.setter
    def regist_id(self, value: int):
        self._regist_id = value

    @property
    def sync_key(self) -> int:
        """同步密钥"""
        return self._sync_key

    @sync_key.setter
    def sync_key(self, value: int):
        self._sync_key = value
