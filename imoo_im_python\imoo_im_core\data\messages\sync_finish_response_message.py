"""
同步完成响应消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(14)
class SyncFinishResponseMessage(BaseMessage):
    """
    同步完成响应消息类

    服务器返回的同步完成响应
    """

    def __init__(self):
        super().__init__()
        self._code: int = 0
        self._desc: str = ""
        self._im_account_id: int = 0
        self._regist_id: int = 0
        self._sync_key: int = 0

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'code': 2,
            'desc': 3,
            'im_account_id': 11,
            'regist_id': 12,
            'sync_key': 13
        }

    @property
    def code(self) -> int:
        """响应代码"""
        return self._code

    @code.setter
    def code(self, value: int):
        self._code = value

    @property
    def desc(self) -> str:
        """响应描述"""
        return self._desc

    @desc.setter
    def desc(self, value: str):
        self._desc = value

    @property
    def im_account_id(self) -> int:
        """IM账户ID"""
        return self._im_account_id

    @im_account_id.setter
    def im_account_id(self, value: int):
        self._im_account_id = value

    @property
    def regist_id(self) -> int:
        """注册ID"""
        return self._regist_id

    @regist_id.setter
    def regist_id(self, value: int):
        self._regist_id = value

    @property
    def sync_key(self) -> int:
        """同步密钥"""
        return self._sync_key

    @sync_key.setter
    def sync_key(self, value: int):
        self._sync_key = value
