﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Data.Account.DeviceInfo
// Assembly: iMooIm.Core, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

#nullable enable
namespace iMooIm.Core.Data.Account;

public class DeviceInfo
{
  public string BasebandVersion { get; set; } = "";

  public string BuildNumber { get; set; } = "";

  public string BindNumber { get; set; } = "";

  public string ChipId { get; set; } = "";

  public string AccountId { get; set; } = "";

  public string ModelNumber { get; set; } = "";

  public int SdkVersion { get; set; }

  public string SysName { get; set; } = "";

  public string SysVersion { get; set; } = "";

  public string EebbkKey { get; set; } = "";
}
