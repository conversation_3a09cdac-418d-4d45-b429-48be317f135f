"""
同步响应消息
"""

from ..base_message import BaseMessageWithoutRid
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(31)
class SyncResponseMessage(BaseMessageWithoutRid):
    """
    同步响应消息类
    
    服务器返回的同步消息
    """
    
    def __init__(self):
        super().__init__()
        self.dialog_id: int = 0
        self.im_account_id: int = 0
        self.regist_id: int = 0
        self.msg_type: int = 0
        self.msg: bytes = b""
        self.sync_key: int = 0
        self.msg_id: str = ""
        self.create_time: int = 0
        self.content_type: int = 0
    
    @tag_id(10)
    @property
    def dialog_id_tagged(self):
        return self.dialog_id
    
    @tag_id(11)
    @property
    def im_account_id_tagged(self):
        return self.im_account_id
    
    @tag_id(12)
    @property
    def regist_id_tagged(self):
        return self.regist_id
    
    @tag_id(13)
    @property
    def msg_type_tagged(self):
        return self.msg_type
    
    @tag_id(14)
    @property
    def msg_tagged(self):
        return self.msg
    
    @tag_id(15)
    @property
    def sync_key_tagged(self):
        return self.sync_key
    
    @tag_id(16)
    @property
    def msg_id_tagged(self):
        return self.msg_id
    
    @tag_id(17)
    @property
    def create_time_tagged(self):
        return self.create_time
    
    @tag_id(18)
    @property
    def content_type_tagged(self):
        return self.content_type
