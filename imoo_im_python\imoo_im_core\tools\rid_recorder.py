"""
请求ID记录器
"""


class RIdRecorder:
    """
    请求ID记录器类

    管理请求ID的生成和记录
    """

    _rid: int = 1005

    @classmethod
    def get_r_id(cls) -> int:
        """
        获取下一个请求ID

        Returns:
            下一个可用的请求ID
        """
        cls._rid += 1
        return cls._rid - 1

    @classmethod
    def set_r_id(cls, value: int):
        """
        设置请求ID

        Args:
            value: 要设置的请求ID值
        """
        cls._rid = value
