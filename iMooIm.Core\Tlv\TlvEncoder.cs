﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Tlv.TlvEncoder
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using System;

#nullable enable
namespace iMooIm.Core.Tlv;

internal static class TlvEncoder
{
  internal static byte[] EncodeTlv(int tag, int length, byte[] value)
  {
    byte[] src1 = TlvEncoder.EncodeTag(0, 0, tag);
    byte[] src2 = TlvEncoder.EncodeLength(length);
    byte[] dst = new byte[src1.Length + src2.Length + value.Length];
    Buffer.BlockCopy((Array) src1, 0, (Array) dst, 0, src1.Length);
    Buffer.BlockCopy((Array) src2, 0, (Array) dst, src1.Length, src2.Length);
    Buffer.BlockCopy((Array) value, 0, (Array) dst, src1.Length + src2.Length, value.Length);
    return dst;
  }

  internal static byte[] EncodeNestedTlv(int dataType, int tag, int length, byte[] value)
  {
    byte[] src1 = TlvEncoder.EncodeTag(dataType, 0, tag);
    byte[] src2 = TlvEncoder.EncodeLength(length);
    byte[] dst = new byte[src1.Length + src2.Length + value.Length];
    Buffer.BlockCopy((Array) src1, 0, (Array) dst, 0, src1.Length);
    Buffer.BlockCopy((Array) src2, 0, (Array) dst, src1.Length, src2.Length);
    Buffer.BlockCopy((Array) value, 0, (Array) dst, src1.Length + src2.Length, value.Length);
    return dst;
  }

  private static byte[] EncodeLength(int i)
  {
    if (i < 0)
      throw new ArgumentException("the length must not less than 0.");
    if (i < 128 /*0x80*/)
      return new byte[1]
      {
        (byte) (i & (int) sbyte.MaxValue)
      };
    int i2 = (int) Math.Ceiling(Math.Log((double) i + 1.0) / Math.Log(128.0));
    return LengthEncode(TlvEncoder.EncodeVariableLengthInteger(0, i2, i), i2);

    static byte[] LengthEncode(int i, int i2)
    {
      byte[] numArray = new byte[i2];
      int length = numArray.Length;
      numArray[0] = (byte) (i >> (i2 - 1) * 8 & (int) byte.MaxValue);
      for (int index = 1; index < length; ++index)
        numArray[index] = (byte) (i >> (i2 - index - 1) * 8 & (int) byte.MaxValue);
      return numArray;
    }
  }

  private static int EncodeVariableLengthInteger(int i, int i2, int i3)
  {
    int num1 = 0;
    int num2;
    while (true)
    {
      num2 = i2 - 1;
      if (num1 < num2)
      {
        i |= (i3 >> num1 * 7 & (int) sbyte.MaxValue | 128 /*0x80*/) << (num2 - num1) * 8;
        ++num1;
      }
      else
        break;
    }
    return i | i3 >> num2 * 7 & (int) sbyte.MaxValue;
  }

  private static byte[] EncodeTag(int i, int i2, int i3)
  {
    int num1 = i | i2;
    int num2 = num1 | i3;
    int i2_1;
    int i1;
    if (i3 >= 31 /*0x1F*/)
    {
      i2_1 = (int) Math.Ceiling(Math.Log((double) i3 + 1.0) / Math.Log(128.0));
      i1 = TlvEncoder.EncodeVariableLengthInteger((num1 | 128 /*0x80*/) << i2_1 * 8, i2_1, i3);
    }
    else
    {
      i1 = num2;
      i2_1 = 0;
    }
    return TlvEncoder.IntToBytes(i1, i2_1);
  }

  private static byte[] IntToBytes(int i, int i2)
  {
    byte[] bytes = new byte[i2 + 1];
    int length = bytes.Length;
    bytes[0] = (byte) (i >> i2 * 8 & (int) byte.MaxValue);
    for (int index = 0; index < length; ++index)
      bytes[index] = (byte) (i >> (i2 - index) * 8 & (int) byte.MaxValue);
    return bytes;
  }
}
