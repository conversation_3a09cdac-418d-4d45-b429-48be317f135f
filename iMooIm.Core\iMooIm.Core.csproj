﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Project was exported from assembly: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll-->
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{143A810D-45BF-4857-AE6D-52692AB758E0}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AssemblyName>iMooIm.Core</AssemblyName>
    <TargetFrameworkVersion>v9.0</TargetFrameworkVersion>
    <ApplicationVersion>1.0.0.0</ApplicationVersion>
    <FileAlignment>512</FileAlignment>
    <RootNamespace>iMooIm.Core</RootNamespace>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Cryptography" />
    <Reference Include="System.Collections" />
    <Reference Include="System.Linq" />
    <Reference Include="System.Net.Primitives" />
    <Reference Include="System.Net.Sockets" />
    <Reference Include="System.Runtime" />
    <Reference Include="System.Text.Json" />
    <Reference Include="System.Text.RegularExpressions" />
    <Reference Include="System.Threading" />
    <Reference Include="System.Threading.Thread" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ImClient.cs" />
    <Compile Include="TcpMessage.cs" />
    <Compile Include="TcpConnection.cs" />
    <Compile Include="Tools\EncryptUtil.cs" />
    <Compile Include="Tools\IntEncoder.cs" />
    <Compile Include="Tools\RIdRecorder.cs" />
    <Compile Include="Tools\SyncKeyManager.cs" />
    <Compile Include="Tlv\TLVByteBuffer.cs" />
    <Compile Include="Tlv\TlvDecoder.cs" />
    <Compile Include="Tlv\TlvDecodeResult.cs" />
    <Compile Include="Tlv\TlvEncoder.cs" />
    <Compile Include="Tlv\TlvUtils.cs" />
    <Compile Include="Message\EncryptWrapper.cs" />
    <Compile Include="Message\Response\AccountResponseMessage.cs" />
    <Compile Include="Message\Response\EncryptSetResponseMessage.cs" />
    <Compile Include="Message\Response\HeartbeatResponseMessage.cs" />
    <Compile Include="Message\Response\LoginResponseMessage.cs" />
    <Compile Include="Message\Response\PublicKeyResponseMessage.cs" />
    <Compile Include="Message\Response\PushResponseMessage.cs" />
    <Compile Include="Message\Response\RegistResponseMessage.cs" />
    <Compile Include="Message\Response\SyncFinishResponseMessage.cs" />
    <Compile Include="Message\Response\SyncInformResponseMessage.cs" />
    <Compile Include="Message\Response\SyncResponseMessage.cs" />
    <Compile Include="Message\Request\AccountRequestMessage.cs" />
    <Compile Include="Message\Request\EncryptSetRequestMessage.cs" />
    <Compile Include="Message\Request\HeartbeatRequestMessage.cs" />
    <Compile Include="Message\Request\LoginRequestMessage.cs" />
    <Compile Include="Message\Request\PublicKeyRequestMessage.cs" />
    <Compile Include="Message\Request\PushResponseAckRequestMessage.cs" />
    <Compile Include="Message\Request\RegistRequestMessage.cs" />
    <Compile Include="Message\Request\SingleMessageRequestMessage.cs" />
    <Compile Include="Message\Request\SyncFinishAckRequest.cs" />
    <Compile Include="Message\Request\SyncRequestMessage.cs" />
    <Compile Include="Exceptions\AccountStateException.cs" />
    <Compile Include="Events\LogEvent.cs" />
    <Compile Include="Events\ReceiveMessageEvent.cs" />
    <Compile Include="Events\SaveSyncKeyEvent.cs" />
    <Compile Include="Events\TextMessageEvent.cs" />
    <Compile Include="Data\BaseMessage.cs" />
    <Compile Include="Data\BaseMessageWithOutRid.cs" />
    <Compile Include="Data\BaseRequestParam.cs" />
    <Compile Include="Data\Entity.cs" />
    <Compile Include="Data\MessageDecoder.cs" />
    <Compile Include="Data\Enums\LogLevel.cs" />
    <Compile Include="Data\ChatMessage\TextMessage.cs" />
    <Compile Include="Data\Attributes\CommandId.cs" />
    <Compile Include="Data\Attributes\TagId.cs" />
    <Compile Include="Data\Account\AppInfo.cs" />
    <Compile Include="Data\Account\DeviceInfo.cs" />
    <Compile Include="AssemblyInfo.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>