﻿namespace iMooIm.Core.Data.Tlv;

public class TlvByteBuffer : MemoryStream{
    private static bool _printLog;
    private volatile int _firstTotalSize = 0;
    private volatile int _firstTagSize = 0;
    private volatile int _firstLengthSize = 0;

    public bool HasNextTlvData() {
        bool z = false;
        if (this.Length == 0) {
            return false;
        }
        Compute();
        if (this._firstTotalSize > 0 && this.Length > 0) {
            if (this._firstTotalSize <= this.Length) {
                z = true;
            }
        }
        return z;
    }

    public void Reset() {
        SetLength(0);
        this._firstTotalSize = 0;
        this._firstTagSize = 0;
        this._firstLengthSize = 0;
    }

    public void Close(){
        Dispose();
    }

    public void Write(byte[] bArr, int i, int i2) {
        base.Write(bArr, i, i2);
    }

    public byte[] CutNextTlvData() {
        byte[] bArr;
        bArr = null;
        if (this._firstTotalSize == this.Length) {
            bArr = this.ToArray();
            Reset();
        } else if (this._firstTotalSize < this.Length) {
            byte[] bArr2 = new byte[this.Length - this._firstTotalSize];
            byte[] bArr3 = new byte[this._firstTotalSize];
            Array.Copy(ToArray(), this._firstTotalSize, bArr2, 0, bArr2.Length);
            Array.Copy(ToArray(), 0, bArr3, 0, bArr3.Length);
            Reset();
            Write(bArr2, 0, bArr2.Length);
            bArr = bArr3;
        }
        return bArr;
    }

    private void Compute() {
        if (this.Length > 0) {
            ComputeTagSize();
            ComputeLengthSize();
            ComputeTotalSize();
        }
    }

    private void ComputeTagSize() {
        if (this._firstTagSize == 0) {
            this._firstTagSize = TLVDecoder.GetTagLength(ToArray());
        }
    }

    private void ComputeLengthSize() {
        if (this._firstLengthSize != 0 || this._firstTagSize == 0) {
            return;
        }
        this._firstLengthSize = TLVDecoder.GetLengthFieldLength(ToArray(), this._firstTagSize);
    }

    private void ComputeTotalSize() {
        if (this._firstTagSize <= 0 || this._firstLengthSize <= 0 || this._firstTotalSize != 0) {
            return;
        }
        byte[] bArr = new byte[this._firstLengthSize];
        Array.Copy(ToArray(), this._firstTagSize, bArr, 0, this._firstLengthSize);
        this._firstTotalSize = this._firstTagSize + this._firstLengthSize + TLVDecoder.GetValueLength(bArr);
    }
    
}