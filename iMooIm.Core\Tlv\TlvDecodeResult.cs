﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Tlv.TLVDecodeResult
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

#nullable enable
namespace iMooIm.Core.Tlv;

internal class TLVDecodeResult
{
  internal int FrameType { get; set; }

  internal object Value { get; set; } = new object();

  internal int DataType { get; set; }

  internal int TagValue { get; set; }

  internal int Length { get; set; }
}
