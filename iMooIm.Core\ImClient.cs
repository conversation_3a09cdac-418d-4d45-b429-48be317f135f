﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.ImClient
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Account;
using iMooIm.Core.Data.ChatMessage;
using iMooIm.Core.Data.Enums;
using iMooIm.Core.Events;
using iMooIm.Core.Exceptions;
using iMooIm.Core.Message.Request;
using iMooIm.Core.Message.Response;
using iMooIm.Core.Tools;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;

#nullable enable
namespace iMooIm.Core;

public class ImClient(DeviceInfo deviceInfo, AppInfo appInfo, long syncKey)
{
  private TcpConnection? _tcpConnection;
  private Thread? _heartbeatThread;
  private long _imAccountId;
  private long _registId;
  private bool _loginState;

  public event EventHandler<LogEvent>? OnLog;

  public event EventHandler<TextMessageEvent>? OnReceiveTextMessage;

  public event EventHandler<SaveSyncKeyEvent>? OnSaveSyncKeyEvent;

  [DoesNotReturn]
  private void Heartbeat()
  {
    while (true)
    {
      this._tcpConnection?.SendWithoutCheck(new HeartbeatRequestMessage().Encode());
      Thread.Sleep(60000);
    }
  }

  private void OnReceiveMessage(object? sender, ReceiveMessageEvent receiveMessageEvent)
  {
    try
    {
      if (!(receiveMessageEvent.Message is PushResponseMessage message))
        return;
      PushResponseAckRequestMessage ackRequestMessage = new PushResponseAckRequestMessage();
      ackRequestMessage.RId = RIdRecorder.RId;
      ackRequestMessage.RegistId = this._registId;
      ackRequestMessage.ImAccountId = this._imAccountId;
      ackRequestMessage.SyncKey = message.SyncKey;
      this._tcpConnection?.Send(ackRequestMessage.Encode());
      if (message.MsgType != 1)
        return;
      TextMessage textMessage = JsonSerializer.Deserialize<TextMessage>(Encoding.UTF8.GetString(message.Msg));
      EventHandler<TextMessageEvent> receiveTextMessage = this.OnReceiveTextMessage;
      if (receiveTextMessage != null)
        receiveTextMessage((object) this, new TextMessageEvent()
        {
          Content = Regex.Unescape(textMessage?.content ?? ""),
          DialogId = message.DialogId,
          FriendId = message.ImAccountId
        });
    }
    catch (Exception ex)
    {
      EventHandler<LogEvent> onLog = this.OnLog;
      if (onLog == null)
        return;
      onLog((object) this, new LogEvent()
      {
        Level = LogLevel.Error,
        Message = ex.Message
      });
    }
  }

  private void Sync()
  {
    EventHandler<SaveSyncKeyEvent> saveSyncKeyEvent1 = this.OnSaveSyncKeyEvent;
    if (saveSyncKeyEvent1 != null)
      saveSyncKeyEvent1((object) this, new SaveSyncKeyEvent()
      {
        SyncKey = SyncKeyManager.ServerSyncKey
      });
    AutoResetEvent finishLock = new AutoResetEvent(false);
    while (this._tcpConnection != null)
    {
      SyncRequestMessage syncRequestMessage1 = new SyncRequestMessage();
      syncRequestMessage1.RId = RIdRecorder.RId;
      syncRequestMessage1.RegistId = this._registId;
      syncRequestMessage1.ImAccountId = this._imAccountId;
      syncRequestMessage1.PageSize = 20;
      syncRequestMessage1.SyncKey = SyncKeyManager.MsgSyncKey == 0L ? SyncKeyManager.ServerSyncKey : SyncKeyManager.MsgSyncKey;
      SyncRequestMessage syncRequestMessage2 = syncRequestMessage1;
      this._tcpConnection.OnReceiveSyncFinishMessage += new EventHandler<ReceiveMessageEvent>(ProcessSyncFinishMsg);
      this._tcpConnection.OnReceiveSyncMessage += new EventHandler<ReceiveMessageEvent>(ProcessSyncMsg);
      this._tcpConnection.Send(syncRequestMessage2.Encode());
      finishLock.WaitOne();
      EventHandler<LogEvent> onLog = this.OnLog;
      if (onLog != null)
        onLog((object) this, new LogEvent()
        {
          Level = LogLevel.Trace,
          Message = "Sync Finished"
        });
      if (SyncKeyManager.MsgSyncKey == SyncKeyManager.ServerSyncKey || SyncKeyManager.MsgSyncKey == 0L)
      {
        EventHandler<SaveSyncKeyEvent> saveSyncKeyEvent2 = this.OnSaveSyncKeyEvent;
        if (saveSyncKeyEvent2 == null)
          break;
        saveSyncKeyEvent2((object) this, new SaveSyncKeyEvent()
        {
          SyncKey = SyncKeyManager.ServerSyncKey
        });
        break;
      }
    }

    void ProcessSyncMsg(object? sender, ReceiveMessageEvent receiveMessageEvent)
    {
      SyncKeyManager.MsgSyncKey = receiveMessageEvent.Message is SyncResponseMessage message ? message.SyncKey : SyncKeyManager.MsgSyncKey;
      SyncKeyManager.ServerSyncKey = Math.Max(SyncKeyManager.MsgSyncKey, SyncKeyManager.ServerSyncKey);
      if (message == null || message.MsgType != 1)
        return;
      TextMessage textMessage = JsonSerializer.Deserialize<TextMessage>(Encoding.UTF8.GetString(message.Msg));
      EventHandler<TextMessageEvent> receiveTextMessage = this.OnReceiveTextMessage;
      if (receiveTextMessage != null)
        receiveTextMessage((object) this, new TextMessageEvent()
        {
          Content = Regex.Unescape(textMessage?.content ?? ""),
          DialogId = message.DialogId,
          FriendId = message.ImAccountId
        });
    }

    void ProcessSyncFinishMsg(object? sender, ReceiveMessageEvent receiveMessageEvent)
    {
      SyncFinishAckRequest finishAckRequest = new SyncFinishAckRequest();
      finishAckRequest.RId = RIdRecorder.RId;
      finishAckRequest.RegistId = this._registId;
      finishAckRequest.ImAccountId = this._imAccountId;
      finishAckRequest.SyncKey = SyncKeyManager.MsgSyncKey;
      this._tcpConnection?.Send(finishAckRequest.Encode());
      finishLock.Set();
    }
  }

  public void Init()
  {
    SyncKeyManager.MsgSyncKey = syncKey;
    this._tcpConnection = new TcpConnection();
    MessageDecoder.OnLog += (EventHandler<LogEvent>) ((sender, args) =>
    {
      EventHandler<LogEvent> onLog = this.OnLog;
      if (onLog == null)
        return;
      onLog(sender, args);
    });
    this._tcpConnection.OnLog += (EventHandler<LogEvent>) ((sender, args) =>
    {
      EventHandler<LogEvent> onLog = this.OnLog;
      if (onLog == null)
        return;
      onLog(sender, args);
    });
    MessageDecoder.Init();
    this._tcpConnection.Init("gw.im.okii.com", 8000);
    EventHandler<LogEvent> onLog1 = this.OnLog;
    if (onLog1 == null)
      return;
    onLog1((object) this, new LogEvent()
    {
      Message = "Client Initialized",
      Level = LogLevel.Information
    });
  }

  public void Login()
  {
    PublicKeyRequestMessage keyRequestMessage = new PublicKeyRequestMessage();
    keyRequestMessage.RId = 1000;
    this._tcpConnection?.Send(keyRequestMessage.Encode());
    PublicKeyResponseMessage response1 = this._tcpConnection?.GetResponse<PublicKeyResponseMessage>(1000);
    EventHandler<LogEvent> onLog1 = this.OnLog;
    if (onLog1 != null)
      onLog1((object) this, new LogEvent()
      {
        Level = LogLevel.Debug,
        Message = "Got rsa key:" + response1?.PublicKey
      });
    if (response1 != null && response1.Code == 200)
    {
      EventHandler<LogEvent> onLog2 = this.OnLog;
      if (onLog2 != null)
        onLog2((object) this, new LogEvent()
        {
          Level = LogLevel.Information,
          Message = "Phase 1: GetRsaKey SUCCESS"
        });
      EncryptSetRequestMessage setRequestMessage = new EncryptSetRequestMessage();
      setRequestMessage.RId = 1001;
      setRequestMessage.EncryptKey = EncryptUtil.EncryptKey(TcpConnection.EncryptKey, response1.PublicKey);
      setRequestMessage.EncryptType = 1;
      this._tcpConnection?.Send(setRequestMessage.Encode());
      EncryptSetResponseMessage response2 = this._tcpConnection?.GetResponse<EncryptSetResponseMessage>(1001);
      if (response2 != null && response2.Code == 200)
      {
        EventHandler<LogEvent> onLog3 = this.OnLog;
        if (onLog3 != null)
          onLog3((object) this, new LogEvent()
          {
            Level = LogLevel.Information,
            Message = "Phase 2: EncryptSet SUCCESS"
          });
        RegistRequestMessage registRequestMessage = new RegistRequestMessage();
        registRequestMessage.RId = 1002;
        registRequestMessage.AppKey = appInfo.AppKey;
        registRequestMessage.BasebandVersion = deviceInfo.BasebandVersion;
        registRequestMessage.BuildNumber = deviceInfo.BuildNumber;
        registRequestMessage.DeviceId = deviceInfo.BindNumber;
        registRequestMessage.ModelNumber = deviceInfo.ModelNumber;
        registRequestMessage.PkgName = "com.xtc.i3launcher";
        registRequestMessage.Platform = 8;
        registRequestMessage.Resolution = "320*360";
        registRequestMessage.SdkVerison = deviceInfo.SdkVersion;
        registRequestMessage.SysName = deviceInfo.SysName;
        registRequestMessage.SysVersion = deviceInfo.SysVersion;
        this._tcpConnection?.Send(registRequestMessage.Encode());
        RegistResponseMessage response3 = this._tcpConnection?.GetResponse<RegistResponseMessage>(1002);
        if (response3 != null && response3.Code == 200)
        {
          EventHandler<LogEvent> onLog4 = this.OnLog;
          if (onLog4 != null)
            onLog4((object) this, new LogEvent()
            {
              Level = LogLevel.Information,
              Message = "Phase 3: Regist SUCCESS"
            });
          long totalMilliseconds = (long) (DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;
          BaseRequestParam baseRequestParam = new BaseRequestParam()
          {
            accountId = deviceInfo.AccountId,
            deviceId = deviceInfo.BindNumber,
            registId = response3.RegistId,
            token = deviceInfo.ChipId
          };
          LoginRequestMessage loginRequestMessage = new LoginRequestMessage();
          loginRequestMessage.RId = 1003;
          loginRequestMessage.RegistId = response3.RegistId;
          loginRequestMessage.RegistToken = response3.RegistToken;
          loginRequestMessage.Platform = 8;
          loginRequestMessage.SdkVersion = 210L;
          loginRequestMessage.ImSdkVersionName = "2.1.0";
          loginRequestMessage.Timestamp = totalMilliseconds;
          loginRequestMessage.EebbkKey = deviceInfo.EebbkKey;
          loginRequestMessage.BaseRequestParam = JsonSerializer.Serialize<BaseRequestParam>(baseRequestParam);
          this._tcpConnection?.Send(loginRequestMessage.Encode());
          LoginResponseMessage response4 = this._tcpConnection?.GetResponse<LoginResponseMessage>(1003);
          if (response4 != null && response4.Code == 200)
          {
            EventHandler<LogEvent> onLog5 = this.OnLog;
            if (onLog5 != null)
              onLog5((object) this, new LogEvent()
              {
                Level = LogLevel.Information,
                Message = "Phase 4: Login SUCCESS"
              });
            AccountRequestMessage accountRequestMessage = new AccountRequestMessage();
            accountRequestMessage.AppKey = appInfo.AppKey;
            accountRequestMessage.Status = 2;
            accountRequestMessage.RId = 1004;
            accountRequestMessage.RegistId = response3.RegistId;
            accountRequestMessage.BusinessId = deviceInfo.AccountId;
            accountRequestMessage.BusinessType = 1;
            accountRequestMessage.BusinessToken = deviceInfo.ChipId;
            accountRequestMessage.LowPower = 2;
            this._tcpConnection?.Send(accountRequestMessage.Encode());
            AccountResponseMessage response5 = this._tcpConnection?.GetResponse<AccountResponseMessage>(1004);
            if (response5 != null && response5.Code == 200)
            {
              EventHandler<LogEvent> onLog6 = this.OnLog;
              if (onLog6 != null)
                onLog6((object) this, new LogEvent()
                {
                  Level = LogLevel.Information,
                  Message = "Phase 5: Set account status to online SUCCESS"
                });
              this._imAccountId = response5.ImAccountId;
              this._registId = response3.RegistId;
              if (this._tcpConnection == null)
              {
                EventHandler<LogEvent> onLog7 = this.OnLog;
                if (onLog7 != null)
                  onLog7((object) this, new LogEvent()
                  {
                    Level = LogLevel.Critical,
                    Message = "TCP connection is null!!!"
                  });
                throw new Exception("TCP connection is null.");
              }
              this._tcpConnection.OnReceiveMessage += new EventHandler<ReceiveMessageEvent>(this.OnReceiveMessage);
              this._tcpConnection.OnNeedSync += (EventHandler<ReceiveMessageEvent>) ((_1, _2) => new Thread(new ThreadStart(this.Sync)).Start());
              this._heartbeatThread = new Thread(new ThreadStart(this.Heartbeat));
              this._heartbeatThread.Start();
              this._loginState = true;
            }
            else
            {
              EventHandler<LogEvent> onLog8 = this.OnLog;
              if (onLog8 == null)
                return;
              onLog8((object) this, new LogEvent()
              {
                Level = LogLevel.Critical,
                Message = "Phase 5: Set account status to online FAIL"
              });
            }
          }
          else
          {
            EventHandler<LogEvent> onLog9 = this.OnLog;
            if (onLog9 == null)
              return;
            onLog9((object) this, new LogEvent()
            {
              Level = LogLevel.Critical,
              Message = "Phase 4: Login FAIL"
            });
          }
        }
        else
        {
          EventHandler<LogEvent> onLog10 = this.OnLog;
          if (onLog10 == null)
            return;
          onLog10((object) this, new LogEvent()
          {
            Level = LogLevel.Critical,
            Message = "Phase 3: Regist FAIL"
          });
        }
      }
      else
      {
        EventHandler<LogEvent> onLog11 = this.OnLog;
        if (onLog11 == null)
          return;
        onLog11((object) this, new LogEvent()
        {
          Level = LogLevel.Critical,
          Message = "Phase 2: EncryptSet FAIL"
        });
      }
    }
    else
    {
      EventHandler<LogEvent> onLog12 = this.OnLog;
      if (onLog12 == null)
        return;
      onLog12((object) this, new LogEvent()
      {
        Level = LogLevel.Critical,
        Message = "Phase 1: GetRsaKey FAIL"
      });
    }
  }

  public void SendTextMsg()
  {
    if (!this._loginState)
      throw new AccountStateException("Account does not login.");
    SingleMessageRequestMessage messageRequestMessage = new SingleMessageRequestMessage()
    {
      AccountId = this._imAccountId,
      ContentType = 3
    };
  }
}
