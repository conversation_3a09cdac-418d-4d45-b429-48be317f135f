﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Data.MessageDecoder
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data.Attributes;
using iMooIm.Core.Data.Enums;
using iMooIm.Core.Events;
using iMooIm.Core.Message;
using iMooIm.Core.Tlv;
using iMooIm.Core.Tools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;

#nullable enable
namespace iMooIm.Core.Data;

internal static class MessageDecoder
{
  private static bool InitState = false;
  private static Dictionary<int, Type> CommandMap = new Dictionary<int, Type>();

  internal static event EventHandler<LogEvent>? OnLog;

  internal static void Init()
  {
    EventHandler<LogEvent> onLog = MessageDecoder.OnLog;
    if (onLog != null)
      onLog((object) null, new LogEvent()
      {
        Message = "MessageDecoder Initialized",
        Level = LogLevel.Information
      });
    string @namespace = "iMooIm.Core.Message";
    foreach (Type element in GetClassesInNamespace(Assembly.GetExecutingAssembly(), @namespace))
    {
      foreach (Attribute customAttribute in element.GetCustomAttributes())
      {
        int key = -1;
        if (customAttribute is CommandId commandId)
          key = commandId.Id;
        if (key != -1)
          MessageDecoder.CommandMap.Add(key, element);
      }
    }
    MessageDecoder.InitState = true;

    static IEnumerable<Type> GetClassesInNamespace(Assembly assembly, string @namespace)
    {
      return ((IEnumerable<Type>) assembly.GetTypes()).Where<Type>((Func<Type, bool>) (t =>
      {
        if (string.Equals(t.Namespace, @namespace, StringComparison.Ordinal))
          return true;
        string str = t.Namespace;
        return str != null && str.StartsWith(@namespace + ".", StringComparison.Ordinal);
      })).Where<Type>((Func<Type, bool>) (t => t.IsClass && !t.IsAbstract));
    }
  }

  internal static object Decode(byte[] data)
  {
    if (!MessageDecoder.InitState)
      throw new InvalidOperationException("MessageDecoder not initialized");
    TLVDecodeResult tlvDecodeResult = TLVDecoder.Decode(data);
    if (!MessageDecoder.CommandMap.ContainsKey(tlvDecodeResult.TagValue))
    {
      EventHandler<LogEvent> onLog = MessageDecoder.OnLog;
      if (onLog != null)
        onLog((object) null, new LogEvent()
        {
          Message = "Unknown command: " + tlvDecodeResult.TagValue.ToString(),
          Level = LogLevel.Warning
        });
      return new object();
    }
    object instance = Activator.CreateInstance(MessageDecoder.CommandMap[tlvDecodeResult.TagValue]);
    IEnumerable<Attribute> customAttributes = instance.GetType().GetCustomAttributes();
    int num = 0;
    foreach (Attribute attribute in customAttributes)
    {
      if (attribute is CommandId commandId)
        num = commandId.Id;
    }
    if (num != tlvDecodeResult.TagValue)
      throw new ArgumentException("CommandId does not match");
    foreach (PropertyInfo property in instance.GetType().GetProperties())
    {
      int tagId = -1;
      TagId customAttribute = property.GetCustomAttribute<TagId>();
      if (customAttribute != null)
        tagId = customAttribute.Id;
      if (tagId == -1)
        throw new ArgumentException("TagId not found");
      object obj1 = tlvDecodeResult.Value is List<TLVDecodeResult> source ? source.FirstOrDefault<TLVDecodeResult>((Func<TLVDecodeResult, bool>) (x => x.TagValue == tagId))?.Value : (object) null;
      Type propertyType = property.PropertyType;
      if (propertyType == typeof (byte[]))
      {
        PropertyInfo propertyInfo = property;
        object obj2 = instance;
        if (!(obj1 is byte[] numArray))
          numArray = Array.Empty<byte>();
        propertyInfo.SetValue(obj2, (object) numArray);
      }
      else if (propertyType == typeof (string))
      {
        PropertyInfo propertyInfo = property;
        object obj3 = instance;
        Encoding utF8 = Encoding.UTF8;
        if (!(obj1 is byte[] bytes))
          bytes = Array.Empty<byte>();
        string str = utF8.GetString(bytes);
        propertyInfo.SetValue(obj3, (object) str);
      }
      else if (propertyType == typeof (int))
      {
        if (!(obj1 is byte[] numArray))
          numArray = Array.Empty<byte>();
        byte[] arr = numArray;
        property.SetValue(instance, (object) MessageDecoder.DecodeToInt(arr));
      }
      else if (propertyType == typeof (long))
      {
        if (!(obj1 is byte[] numArray))
          numArray = Array.Empty<byte>();
        byte[] arr = numArray;
        property.SetValue(instance, (object) MessageDecoder.DecodeToLong(arr));
      }
    }
    return instance is EncryptWrapper encryptWrapper ? MessageDecoder.Decode(EncryptUtil.Encrypt(encryptWrapper.Payload, TcpConnection.EncryptKey)) : instance;
  }

  private static long DecodeToLong(byte[] arr)
  {
    if (BitConverter.IsLittleEndian)
      Array.Reverse<byte>(arr);
    switch (arr.Length)
    {
      case 0:
        return 0;
      case 1:
        return (long) arr[0];
      case 2:
        return (long) BitConverter.ToInt16(arr, 0);
      case 4:
        return (long) BitConverter.ToInt32(arr, 0);
      case 8:
        return BitConverter.ToInt64(arr, 0);
      default:
        throw new ArgumentException("Invalid array length");
    }
  }

  private static int DecodeToInt(byte[] arr)
  {
    if (BitConverter.IsLittleEndian)
      Array.Reverse<byte>(arr);
    switch (arr.Length)
    {
      case 0:
        return 0;
      case 1:
        return (int) arr[0];
      case 2:
        return (int) BitConverter.ToInt16(arr, 0);
      case 4:
        return BitConverter.ToInt32(arr, 0);
      default:
        throw new ArgumentException("Invalid array length:" + arr.Length.ToString());
    }
  }
}
