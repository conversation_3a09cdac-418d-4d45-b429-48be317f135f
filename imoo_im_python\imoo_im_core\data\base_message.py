"""
基础消息类 - 所有消息类型的基类
"""

from abc import ABC
from typing import Dict, Any
from .entity import Entity
from .attributes.command_id import get_command_id
from .attributes.tag_id import get_all_tag_ids, tag_id


class BaseMessage(ABC):
    """
    基础消息类，包含 RId 字段
    """

    def __init__(self):
        self._r_id: int = -1
        # 设置标签 ID 映射
        if not hasattr(self.__class__, '_tag_ids'):
            self.__class__._tag_ids = {'r_id': 1}

    @property
    def r_id(self) -> int:
        """请求 ID"""
        return self._r_id

    @r_id.setter
    def r_id(self, value: int):
        self._r_id = value
    
    def encode(self) -> bytes:
        """
        编码消息为字节数组
        
        Returns:
            编码后的字节数组
        """
        entity = Entity()
        
        # 获取命令 ID
        command = get_command_id(self.__class__)
        entity.command_id = command
        
        # 获取所有标签 ID 映射
        tag_ids = get_all_tag_ids(self.__class__)
        
        # 编码所有属性 - 只跳过空字符串（与C#版本行为一致）
        for attr_name, tag in tag_ids.items():
            if hasattr(self, attr_name):
                value = getattr(self, attr_name)
                # 只跳过空字符串，保留数字0（因为0可能是有效值）
                if value != "":
                    entity.data[tag] = value
            elif hasattr(self, f"_{attr_name}"):
                value = getattr(self, f"_{attr_name}")
                # 只跳过空字符串，保留数字0（因为0可能是有效值）
                if value != "":
                    entity.data[tag] = value
        
        # 特殊处理 r_id
        entity.data[1] = self.r_id
        
        return entity.encode()


class BaseMessageWithoutRid(ABC):
    """
    不包含 RId 字段的基础消息类
    """

    def __init__(self):
        # 设置标签 ID 映射
        if not hasattr(self.__class__, '_tag_ids'):
            self.__class__._tag_ids = {}

    def encode(self) -> bytes:
        """
        编码消息为字节数组
        
        Returns:
            编码后的字节数组
        """
        entity = Entity()
        
        # 获取命令 ID
        command = get_command_id(self.__class__)
        entity.command_id = command
        
        # 获取所有标签 ID 映射
        tag_ids = get_all_tag_ids(self.__class__)
        
        # 编码所有属性
        for attr_name, tag in tag_ids.items():
            if hasattr(self, attr_name):
                value = getattr(self, attr_name)
                entity.data[tag] = value
            elif hasattr(self, f"_{attr_name}"):
                value = getattr(self, f"_{attr_name}")
                entity.data[tag] = value
        
        return entity.encode()
