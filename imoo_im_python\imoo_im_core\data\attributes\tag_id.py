"""
标签 ID 装饰器 - 用于标识属性的标签 ID
"""

from typing import Any, Dict


def tag_id(id_value: int):
    """
    标签 ID 装饰器，用于标识属性的标签 ID
    
    Args:
        id_value: 标签 ID 值
    """
    def decorator(func):
        func._tag_id = id_value
        return func
    return decorator


def get_tag_id(obj: Any, attr_name: str) -> int:
    """
    获取对象属性的标签 ID
    
    Args:
        obj: 对象实例
        attr_name: 属性名称
        
    Returns:
        标签 ID 值，如果没有设置则返回 -1
    """
    # 首先尝试从类的属性描述符中获取
    cls = obj.__class__
    if hasattr(cls, attr_name):
        attr = getattr(cls, attr_name)
        if hasattr(attr, '_tag_id'):
            return attr._tag_id
    
    # 如果没有找到，检查实例的 __annotations__ 和 _tag_ids
    if hasattr(obj, '_tag_ids') and attr_name in obj._tag_ids:
        return obj._tag_ids[attr_name]
    
    return -1


def set_tag_ids(cls):
    """
    类装饰器，用于设置类的标签 ID 映射
    """
    if not hasattr(cls, '_tag_ids'):
        cls._tag_ids = {}
    
    # 扫描类的所有属性，查找带有 _tag_id 的属性
    for attr_name in dir(cls):
        if not attr_name.startswith('_'):
            attr = getattr(cls, attr_name)
            if hasattr(attr, '_tag_id'):
                cls._tag_ids[attr_name] = attr._tag_id
    
    return cls


def get_all_tag_ids(cls) -> Dict[str, int]:
    """
    获取类的所有标签 ID 映射
    
    Args:
        cls: 要获取标签 ID 的类
        
    Returns:
        属性名到标签 ID 的映射字典
    """
    tag_ids = {}
    
    # 检查类的 _tag_ids 属性
    if hasattr(cls, '_tag_ids'):
        tag_ids.update(cls._tag_ids)
    
    # 扫描类的所有属性
    for attr_name in dir(cls):
        if not attr_name.startswith('_'):
            try:
                attr = getattr(cls, attr_name)
                if hasattr(attr, '_tag_id'):
                    tag_ids[attr_name] = attr._tag_id
            except:
                continue
    
    return tag_ids
