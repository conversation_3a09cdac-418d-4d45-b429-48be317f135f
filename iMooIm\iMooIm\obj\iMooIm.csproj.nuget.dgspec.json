{"format": 1, "restore": {"D:\\桌面\\iMooIm\\iMooIm\\iMooIm.csproj": {}}, "projects": {"D:\\桌面\\iMooIm\\iMooIm.Core\\iMooIm.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\桌面\\iMooIm\\iMooIm.Core\\iMooIm.Core.csproj", "projectName": "iMooIm.Core", "projectPath": "D:\\桌面\\iMooIm\\iMooIm.Core\\iMooIm.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\桌面\\iMooIm\\iMooIm.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\vs2022plugin\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"BouncyCastle.Cryptography": {"target": "Package", "version": "[2.5.1, )"}, "TlvLib": {"target": "Package", "version": "[1.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}, "D:\\桌面\\iMooIm\\iMooIm\\iMooIm.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\桌面\\iMooIm\\iMooIm\\iMooIm.csproj", "projectName": "iMooIm", "projectPath": "D:\\桌面\\iMooIm\\iMooIm\\iMooIm.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\桌面\\iMooIm\\iMooIm\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\vs2022plugin\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\桌面\\iMooIm\\iMooIm.Core\\iMooIm.Core.csproj": {"projectPath": "D:\\桌面\\iMooIm\\iMooIm.Core\\iMooIm.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"BouncyCastle.Cryptography": {"target": "Package", "version": "[2.5.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102/PortableRuntimeIdentifierGraph.json"}}}}}