"""
TLV 解码结果类
"""

from typing import Any
from .tlv_utils import bytes_to_long


class TlvDecodeResult:
    """
    TLV 解码结果，包含解码后的各个字段
    """
    
    def __init__(self):
        self.frame_type: int = 0
        self.value: Any = None
        self.data_type: int = 0
        self.tag_value: int = 0
        self.length: int = 0
    
    def to_int_value(self) -> int:
        """
        将值转换为整数
        
        Returns:
            整数值，如果无法转换则返回 0
        """
        if isinstance(self.value, bytes):
            try:
                return bytes_to_long(self.value)
            except:
                return 0
        return 0
    
    def to_long_value(self) -> int:
        """
        将值转换为长整数
        
        Returns:
            长整数值，如果无法转换则返回 0
        """
        if isinstance(self.value, bytes):
            try:
                return bytes_to_long(self.value)
            except:
                return 0
        return 0
    
    def to_string_value(self) -> str:
        """
        将值转换为字符串
        
        Returns:
            字符串值，如果无法转换则返回空字符串
        """
        if isinstance(self.value, bytes):
            try:
                return self.value.decode('utf-8')
            except:
                return ""
        return ""
