﻿namespace iMooIm.Core.Tools;

public static class IntEncoder
{
    public static byte[] Encode2Bytes(this long value)
    {
        byte[] bytes;
        if (value is >= 0 and <= 255)
        {
            bytes = [(byte)value];
        }
        else if (value is >= 0 and <= 65535)
        {
            bytes = BitConverter.GetBytes((ushort)value);
        }
        else if (value is >= 0 and <= 4294967295)
        {
            bytes = BitConverter.GetBytes((uint)value);
        }
        else
        {
            bytes = BitConverter.GetBytes(value);
        }
        if (BitConverter.IsLittleEndian)
        {
            Array.Reverse(bytes);
        }
        return bytes;
    }
    public static byte[] Encode2Bytes(this int value)
    {
        byte[] bytes;
        if (value is >= 0 and <= 255)
        {
            bytes = [(byte)value];
        }
        else if (value is >= 0 and <= 65535)
        {
            bytes = BitConverter.GetBytes((ushort)value);
        }
        else
        {
            bytes = BitConverter.GetBytes(value);
        }
        if (BitConverter.IsLittleEndian)
        {
            Array.Reverse(bytes);
        }
        return bytes;
    }
}