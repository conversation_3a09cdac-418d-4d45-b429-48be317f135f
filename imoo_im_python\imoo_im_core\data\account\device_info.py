"""
设备信息数据模型
"""


class DeviceInfo:
    """
    设备信息类
    
    包含设备的各种标识和版本信息
    """
    
    def __init__(self):
        self.baseband_version: str = ""
        self.build_number: str = ""
        self.bind_number: str = ""
        self.chip_id: str = ""
        self.account_id: str = ""
        self.model_number: str = ""
        self.sdk_version: int = 0
        self.sys_name: str = ""
        self.sys_version: str = ""
        self.eebbk_key: str = ""
        self.sync_key: int = 0
