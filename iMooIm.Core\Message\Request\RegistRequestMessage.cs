﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Message.Request.RegistRequestMessage
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;

#nullable enable
namespace iMooIm.Core.Message.Request;

[CommandId(1)]
internal class RegistRequestMessage : BaseMessage
{
  [TagId(11)]
  public string AppKey { get; set; } = "";

  [TagId(21)]
  public string BasebandVersion { get; set; } = "";

  [TagId(22)]
  public string BuildNumber { get; set; } = "";

  [TagId(13)]
  public string DeviceId { get; set; } = "";

  [TagId(20)]
  public string ModelNumber { get; set; } = "";

  [TagId(12)]
  public string PkgName { get; set; } = "";

  [TagId(10)]
  public int Platform { get; set; }

  [TagId(23)]
  public string Resolution { get; set; } = "";

  [TagId(14)]
  public int SdkVerison { get; set; }

  [TagId(15)]
  public string SysName { get; set; } = "";

  [TagId(16 /*0x10*/)]
  public string SysVersion { get; set; } = "";
}
