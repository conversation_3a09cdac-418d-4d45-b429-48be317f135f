﻿using System.Reflection;
using iMooIm.Core.Data.Attributes;

namespace iMooIm.Core.Data;
[CommandId(-1)]
public abstract class BaseMessageWithOutRid
{
    public byte[] Encode()
    {
        var entity = new Entity();
        var attrs = GetType().GetCustomAttributes();
        int command = 0;
        foreach (var attr in attrs)
        {
            if (attr is CommandId commandId)
            {
                command = commandId.Id;
            }
        }
        entity.CommandId = command;
        foreach (var prop in GetType().GetProperties())
        {
            var propAttrs = prop.GetCustomAttributes();
            int tag = 0;
            foreach (var attr in propAttrs)
            {
                if (attr is TagId tagId)
                {
                    tag = tagId.Id;
                }
            }
            entity.Data.Add(tag, prop.GetValue(this) ?? 0);
        }
        return entity.Encode();
    }
}