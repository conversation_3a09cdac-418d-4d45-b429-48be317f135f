﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Message.Response.PushResponseMessage
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;
using System;

#nullable enable
namespace iMooIm.Core.Message.Response;

[CommandId(35)]
internal class PushResponseMessage : BaseMessageWithOutRid
{
  [TagId(18)]
  public int ContentType { get; set; }

  [TagId(17)]
  public long CreateTime { get; set; }

  [TagId(10)]
  public long DialogId { get; set; }

  [TagId(11)]
  public long ImAccountId { get; set; }

  [TagId(14)]
  public byte[] Msg { get; set; } = Array.Empty<byte>();

  [TagId(16 /*0x10*/)]
  public string MsgId { get; set; } = "";

  [TagId(13)]
  public int MsgType { get; set; }

  [TagId(12)]
  public long RegistId { get; set; }

  [TagId(15)]
  public long SyncKey { get; set; }
}
