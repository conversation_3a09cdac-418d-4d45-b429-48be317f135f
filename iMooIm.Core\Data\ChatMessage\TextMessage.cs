﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Data.ChatMessage.TextMessage
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using System.Collections.Generic;

#nullable enable
namespace iMooIm.Core.Data.ChatMessage;

internal class TextMessage
{
  internal string content { get; set; } = "";

  internal string isContentSensitive { get; set; } = "";

  internal bool isSensitive { get; set; }

  internal string msgNewContent { get; set; } = "";

  internal int msgNewType { get; set; }

  internal List<int> msgTypeList { get; set; } = new List<int>();

  internal string text { get; set; } = "";

  internal int textMsgType { get; set; }
}
