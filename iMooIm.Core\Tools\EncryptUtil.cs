﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Tools.EncryptUtil
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using Org.BouncyCastle.Asn1.X509;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Security;
using System;

#nullable enable
namespace iMooIm.Core.Tools;

internal static class EncryptUtil
{
  private static byte[] ConvertPemToDer(string pemKey)
  {
    string str1 = "-----BEGIN PUBLIC KEY-----";
    string str2 = "-----END PUBLIC KEY-----";
    int startIndex = pemKey.IndexOf(str1, StringComparison.Ordinal) + str1.Length;
    int num = pemKey.LastIndexOf(str2, StringComparison.Ordinal);
    if (startIndex == -1 || num == -1)
      throw new ArgumentException("Invalid PEM format");
    return Convert.FromBase64String(pemKey.Substring(startIndex, num - startIndex).Replace("\n", "").Replace("\r", ""));
  }

  internal static byte[] EncryptKey(byte[] dataToEncrypt, string publicKeyBytes)
  {
    AsymmetricKeyParameter key = PublicKeyFactory.CreateKey(SubjectPublicKeyInfo.GetInstance((object) EncryptUtil.ConvertPemToDer(publicKeyBytes)));
    IBufferedCipher cipher = CipherUtilities.GetCipher("RSA/ECB/PKCS1Padding");
    cipher.Init(true, (ICipherParameters) key);
    return cipher.DoFinal(dataToEncrypt);
  }

  internal static byte[] Encrypt(byte[] srcData, byte[] secretKey)
  {
    byte[] numArray = new byte[srcData.Length];
    for (int index = 0; index < srcData.Length; ++index)
      numArray[index] = (byte) ((uint) srcData[index] ^ (uint) secretKey[index % secretKey.Length]);
    return numArray;
  }
}
