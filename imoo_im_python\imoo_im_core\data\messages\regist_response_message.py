"""
注册响应消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(2)
class RegistResponseMessage(BaseMessage):
    """注册响应消息，服务器对注册请求的响应"""

    def __init__(self):
        super().__init__()
        self._code: int = 0
        self._desc: str = ""
        self._regist_id: int = 0  # C#版本使用long，但Python的int可以处理任意大小
        self._regist_token: str = ""

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'code': 2,
            'desc': 3,
            'regist_id': 10,
            'regist_token': 11
        }

    @property
    def code(self) -> int:
        """响应代码"""
        return self._code

    @code.setter
    def code(self, value: int):
        self._code = value

    @property
    def desc(self) -> str:
        """响应描述"""
        return self._desc

    @desc.setter
    def desc(self, value: str):
        self._desc = value

    @property
    def regist_id(self) -> int:
        """注册 ID"""
        return self._regist_id

    @regist_id.setter
    def regist_id(self, value: int):
        self._regist_id = value

    @property
    def regist_token(self) -> str:
        """注册令牌"""
        return self._regist_token

    @regist_token.setter
    def regist_token(self, value: str):
        self._regist_token = value
