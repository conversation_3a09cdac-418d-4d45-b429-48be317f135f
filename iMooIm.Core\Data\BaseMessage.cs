﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Data.BaseMessage
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data.Attributes;
using System;
using System.Collections.Generic;
using System.Reflection;

#nullable enable
namespace iMooIm.Core.Data;

internal abstract class BaseMessage
{
  [TagId(1)]
  public int RId { get; set; } = -1;

  internal byte[] Encode()
  {
    Entity entity = new Entity();
    IEnumerable<Attribute> customAttributes1 = this.GetType().GetCustomAttributes();
    int num = 0;
    foreach (Attribute attribute in customAttributes1)
    {
      if (attribute is CommandId commandId)
        num = commandId.Id;
    }
    entity.CommandId = num;
    foreach (PropertyInfo property in this.GetType().GetProperties())
    {
      IEnumerable<Attribute> customAttributes2 = property.GetCustomAttributes();
      int key = 0;
      foreach (Attribute attribute in customAttributes2)
      {
        if (attribute is TagId tagId)
          key = tagId.Id;
      }
      entity.Data.Add(key, property.GetValue((object) this) ?? (object) 0);
    }
    return entity.Encode();
  }
}
