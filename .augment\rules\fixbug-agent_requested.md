---
type: "agent_requested"
description: "在修复编译错误、功能异常、依赖问题等开发问题时启用，包括未定义方法、缺失依赖、类型错误、逻辑错误等场景，要求保持功能完整性，通过系统性分析和逐步修复确保代码质量和项目稳定性"
---

# Bug修复和问题解决规则

## 使用场景
- 遇到编译错误、构建失败时
- 发现功能异常、逻辑错误时
- 出现未定义方法、缺失依赖时
- 需要修复类型错误、语法错误时
- 处理运行时异常、性能问题时
- 解决代码冲突、版本兼容性问题时

## 关键规则
- **始终使用中文回答和交流**
- **绝对禁止删除现有功能或简化代码来"修复"问题**
- **必须在修复前充分了解项目结构和文档**
- **优先查找已实现的相似方法，避免重复开发**
- **采用系统性分析方法，制定完整的修复计划**
- **逐个修复问题，确保每个修复都经过验证**
- **保持代码的完整性和功能的连续性**
- **使用包管理器处理依赖问题，不手动编辑配置文件**

## 修复流程
### 1. 问题分析阶段
- 仔细阅读错误信息和堆栈跟踪
- 查看项目文档（docs目录下的相关文档）
- 分析项目整体结构和架构
- 识别问题的根本原因和影响范围

### 2. 解决方案规划
- 创建详细的任务列表，按优先级排序
- 评估每个问题的修复复杂度
- 确定修复顺序（通常先修复编译错误）
- 制定验证和测试计划

### 3. 实施修复
- 逐个处理问题，避免同时修改多个文件
- 对于未定义方法，先搜索是否有相似实现
- 保持原有代码逻辑和功能不变
- 每次修复后进行编译验证

### 4. 验证和测试
- 确保修复后代码能正常编译
- 验证功能是否按预期工作
- 检查是否引入新的问题
- 建议编写或更新相关测试

## 示例

<example>
  **正确的修复方法：**

  发现大量未定义方法错误时：
  1. 首先使用codebase-retrieval工具搜索相似的已实现方法
  2. 分析未定义方法的预期功能和参数
  3. 创建详细的修复任务列表
  4. 逐个实现缺失的方法，保持与现有代码风格一致
  5. 每个方法实现后进行编译验证

  "我发现代码中有多个未定义方法，让我先搜索项目中是否有相似的实现，然后分析每个方法的功能需求，制定系统性的修复计划。"
</example>

<example type="invalid">
  **错误的修复方法：**

  遇到编译错误时直接删除或注释问题代码：
  1. 看到未定义方法就直接删除调用
  2. 遇到复杂逻辑就简化或移除
  3. 为了快速编译通过而牺牲功能完整性
  4. 不分析问题原因就盲目修改

  "我看到代码中有大量未定义方法，我将删除这些调用或简化功能，以便项目能够编译通过。"
</example>

## 特殊情况处理
### 依赖问题
- 使用适当的包管理器（npm、pip、gradle等）
- 不直接编辑package.json、requirements.txt等配置文件
- 检查版本兼容性和依赖冲突

### 架构问题
- 参考项目的技术架构文档
- 保持与现有架构模式的一致性
- 避免引入不兼容的设计模式

### 性能问题
- 分析性能瓶颈的根本原因
- 优化算法和数据结构
- 考虑缓存和异步处理策略

## 质量保证
- 每次修复都要进行编译验证
- 建议为修复的功能编写测试用例
- 保持代码注释和文档的更新
- 遵循项目的编码规范和最佳实践





