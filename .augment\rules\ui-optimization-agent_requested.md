---
type: "agent_requested"
description: "在进行UI界面优化、样式统一、布局调整、卡片边距规范化等视觉设计改进时启用，确保全局样式一致性、合理布局结构、统一间距规范，通过全面搜索和系统性优化提升应用整体视觉质量和用户体验"
---
# UI优化与样式统一规则 🎨

## 使用场景
- 进行UI界面优化和视觉改进时
- 统一应用样式和设计规范时
- 调整布局结构和间距规范时
- 提升用户界面美观度和一致性时
- 修复UI不一致问题和视觉缺陷时

## 关键规则

### 🎯 全局样式统一
- **始终使用全局样式定义**：所有UI组件必须使用统一的样式资源文件
- **建立完整的设计系统**：创建包含颜色、字体、尺寸、动画的标准化样式库
- **禁止内联样式**：绝不在布局文件中使用硬编码的样式值
- **保持平台一致性**：手机端和电视端在各自平台特色基础上保持视觉统一

### 📐 布局结构优化
- **采用响应式设计**：确保界面在不同屏幕尺寸下都有良好表现
- **遵循Material Design规范**：使用Material 3设计语言和组件
- **优化信息层级**：通过视觉权重和间距建立清晰的信息架构
- **确保无障碍访问**：满足可访问性标准，支持屏幕阅读器

### 📏 卡片边距规范
- **统一外边距**：所有卡片使用16dp外边距
- **统一内间距**：所有卡片使用12dp内间距
- **全面搜索应用**：必须搜索所有布局文件，确保无遗漏
- **建立全局间距常量**：在dimens.xml中定义标准间距值

### 🔍 全面搜索策略
- **搜索所有布局文件**：包括activity、fragment、dialog、item布局
- **检查自定义组件**：确保自定义View和ViewGroup遵循规范
- **审查样式文件**：检查styles.xml、themes.xml、colors.xml等资源文件
- **验证代码中的动态设置**：检查Java/Kotlin代码中的布局参数设置

### 🎨 视觉美学原则
- **保持视觉平衡**：合理分配空白空间，避免界面拥挤
- **统一色彩方案**：使用一致的主色调、辅助色和强调色
- **优化字体层级**：建立清晰的字体大小和权重体系
- **添加微交互**：适当的动画和反馈提升用户体验

## 实施步骤

### 1. 全面审查现状
- 使用codebase-retrieval搜索所有UI相关文件
- 识别样式不一致和布局问题
- 建立问题清单和优化计划

### 2. 建立设计系统
- 创建或完善全局样式资源文件
- 定义标准化的间距、颜色、字体规范
- 建立可复用的UI组件库

### 3. 系统性重构
- 按模块逐步应用新的设计规范
- 替换硬编码样式为全局样式引用
- 统一所有卡片和组件的边距设置

### 4. 质量验证
- 在不同设备和屏幕尺寸上测试
- 验证样式一致性和视觉效果
- 确保无障碍功能正常工作

## 示例

<example>
**正确的卡片边距设置**：
```xml
<androidx.cardview.widget.CardView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/card_margin_standard"
    android:padding="@dimen/card_padding_standard"
    style="@style/AppCardView">
    
    <!-- 卡片内容 -->
    
</androidx.cardview.widget.CardView>
```

**全局样式定义**：
```xml
<!-- dimens.xml -->
<dimen name="card_margin_standard">16dp</dimen>
<dimen name="card_padding_standard">12dp</dimen>

<!-- styles.xml -->
<style name="AppCardView" parent="Widget.Material3.CardView.Elevated">
    <item name="cardCornerRadius">@dimen/card_corner_radius</item>
    <item name="cardElevation">@dimen/card_elevation</item>
</style>
```
</example>

<example type="invalid">
**错误的硬编码样式**：
```xml
<androidx.cardview.widget.CardView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="20dp"
    android:padding="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">
    
    <!-- 不一致的边距和样式 -->
    
</androidx.cardview.widget.CardView>
```

**不统一的间距使用**：
- 某些卡片使用20dp边距，某些使用16dp
- 内间距在8dp到16dp之间变化
- 缺乏全局样式定义和复用
</example>

## 质量标准
- ✅ 所有UI组件使用统一的全局样式
- ✅ 布局在不同屏幕尺寸下表现良好
- ✅ 色彩、字体、间距完全一致
- ✅ 符合Material Design 3规范
- ✅ 支持无障碍访问功能
