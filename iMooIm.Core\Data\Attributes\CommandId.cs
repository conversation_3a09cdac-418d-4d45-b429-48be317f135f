﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Data.Attributes.CommandId
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using System;

#nullable disable
namespace iMooIm.Core.Data.Attributes;

[AttributeUsage(AttributeTargets.Class)]
internal class CommandId(int id) : Attribute
{
  public int Id { get; set; } = id;
}
