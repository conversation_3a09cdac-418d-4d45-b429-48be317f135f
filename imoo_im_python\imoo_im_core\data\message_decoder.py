"""
消息解码器 - 负责将字节数据解码为消息对象
"""

import json
import inspect
from typing import Dict, Type, Any, Optional, List, Callable
from .base_message import BaseMessage, BaseMessageWithoutRid
from .tlv.tlv_decoder import TlvDecoder
from .tlv.tlv_decode_result import TlvDecodeResult
from .attributes.command_id import get_command_id
from .attributes.tag_id import get_all_tag_ids
from ..tools.int_encoder import decode_from_bytes


class MessageDecoder:
    """消息解码器"""

    _init_state = False
    _command_map: Dict[int, Type] = {}
    on_log: Optional[Callable] = None
    
    @classmethod
    def init(cls):
        """
        初始化消息解码器，扫描并注册所有消息类型
        """
        if cls._init_state:
            return
        
        # 导入所有消息类型
        from .messages import (
            EncryptWrapper,
            PublicKeyRequestMessage,
            PublicKeyResponseMessage,
            EncryptSetRequestMessage,
            EncryptSetResponseMessage,
            RegistRequestMessage,
            RegistResponseMessage,
            LoginRequestMessage,
            LoginResponseMessage,
            AccountRequestMessage,
            AccountResponseMessage,
            HeartbeatRequestMessage,
            HeartbeatResponseMessage,
            SyncRequestMessage,
            SyncFinishAckRequest,
            SyncInformResponseMessage,
            PushResponseMessage,
            PushResponseAckRequestMessage,
            SingleMessageRequestMessage
        )
        from .messages.sync_response_message import SyncResponseMessage
        from .messages.sync_finish_response_message import SyncFinishResponseMessage

        # 注册所有消息类型
        message_classes = [
            EncryptWrapper,
            PublicKeyRequestMessage,
            PublicKeyResponseMessage,
            EncryptSetRequestMessage,
            EncryptSetResponseMessage,
            RegistRequestMessage,
            RegistResponseMessage,
            LoginRequestMessage,
            LoginResponseMessage,
            AccountRequestMessage,
            AccountResponseMessage,
            HeartbeatRequestMessage,
            HeartbeatResponseMessage,
            SyncRequestMessage,
            SyncFinishAckRequest,
            SyncInformResponseMessage,
            PushResponseMessage,
            PushResponseAckRequestMessage,
            SingleMessageRequestMessage,
            SyncResponseMessage,
            SyncFinishResponseMessage
        ]
        
        for message_class in message_classes:
            command = get_command_id(message_class)
            if command != -1:
                cls._command_map[command] = message_class
        
        cls._init_state = True
    
    @classmethod
    def decode(cls, data: bytes) -> Optional[Any]:
        """
        解码字节数据为消息对象
        
        Args:
            data: 要解码的字节数据
            
        Returns:
            解码后的消息对象，如果解码失败则返回 None
        """
        if not cls._init_state:
            raise RuntimeError("消息解码器未初始化")
        
        try:
            # 解码 TLV 数据
            decode_result = TlvDecoder.decode(data)
            
            # 调试输出（可选）
            # print(f"Decoded TLV tag_value: {decode_result.tag_value}")
            # print(f"TLV: {json.dumps(cls._tlv_result_to_dict(decode_result), indent=2)}")
            
            # 查找对应的消息类型
            if decode_result.tag_value not in cls._command_map:
                raise ValueError(f"未知命令ID: {decode_result.tag_value}")
            
            message_class = cls._command_map[decode_result.tag_value]
            
            # 验证命令 ID
            expected_command = get_command_id(message_class)
            if expected_command != decode_result.tag_value:
                raise ValueError("命令ID不匹配")
            
            # 创建消息实例
            message = message_class()

            # 获取属性标签映射（从实例中获取，因为映射在 __init__ 中设置）
            tag_ids = getattr(message.__class__, '_tag_ids', {})
            
            # 解析嵌套 TLV 数据
            if isinstance(decode_result.value, list):
                tlv_list = decode_result.value
                
                # 设置属性值
                for attr_name, tag_id in tag_ids.items():
                    tlv_data = cls._find_tlv_by_tag(tlv_list, tag_id)
                    if tlv_data is not None:
                        cls._set_property_value(message, attr_name, tlv_data)
            
            # 处理加密包装器 - 关键的解密逻辑！
            from .messages.encrypt_wrapper import EncryptWrapper
            from ..tcp_connection import TcpConnection
            from ..tools.encrypt_util import EncryptUtil

            if isinstance(message, EncryptWrapper):
                # 解密载荷并递归解码
                decrypted_data = EncryptUtil.encrypt(message.payload, TcpConnection.encrypt_key)
                return cls.decode(decrypted_data)

            return message

        except Exception as e:
            print(f"Failed to decode message: {e}")
            return None
    
    @classmethod
    def _find_tlv_by_tag(cls, tlv_list: List[TlvDecodeResult], tag: int) -> Optional[TlvDecodeResult]:
        """
        在 TLV 列表中查找指定标签的数据
        
        Args:
            tlv_list: TLV 解码结果列表
            tag: 要查找的标签
            
        Returns:
            找到的 TLV 数据，如果没找到则返回 None
        """
        for tlv in tlv_list:
            if tlv.tag_value == tag:
                return tlv
        return None
    
    @classmethod
    def _set_property_value(cls, obj: Any, attr_name: str, tlv_data: TlvDecodeResult):
        """
        设置对象属性值

        Args:
            obj: 目标对象
            attr_name: 属性名
            tlv_data: TLV 数据
        """
        try:
            # 直接根据属性名推断类型并设置值
            if attr_name == 'r_id' or attr_name.endswith('_id') or attr_name.endswith('_type') or attr_name.endswith('_version') or attr_name == 'platform' or attr_name == 'code':
                # 整数类型
                value = cls._decode_to_int(tlv_data.value)
                setattr(obj, attr_name, value)
            elif attr_name == 'encrypt_key' or attr_name == 'payload' or attr_name == 'exponent' or attr_name == 'modulus' or attr_name == 'msg':
                # 字节类型（注意：public_key 是字符串类型）
                if isinstance(tlv_data.value, bytes):
                    setattr(obj, attr_name, tlv_data.value)
                else:
                    setattr(obj, attr_name, b'')
            else:
                # 字符串类型
                value = tlv_data.to_string_value()
                setattr(obj, attr_name, value)

        except Exception as e:
            print(f"设置属性 {attr_name} 失败: {e}")
    
    @classmethod
    def _decode_to_int(cls, data: Any) -> int:
        """
        将数据解码为整数
        
        Args:
            data: 要解码的数据
            
        Returns:
            解码后的整数值
        """
        if isinstance(data, bytes):
            if len(data) == 0:
                return 0
            return decode_from_bytes(data)
        elif isinstance(data, int):
            return data
        else:
            return 0
    
    @classmethod
    def _tlv_result_to_dict(cls, result: TlvDecodeResult) -> Dict[str, Any]:
        """
        将 TLV 解码结果转换为字典（用于调试）
        
        Args:
            result: TLV 解码结果
            
        Returns:
            字典表示
        """
        data = {
            "frame_type": result.frame_type,
            "data_type": result.data_type,
            "tag_value": result.tag_value,
            "length": result.length
        }
        
        if isinstance(result.value, list):
            data["value"] = [cls._tlv_result_to_dict(item) for item in result.value]
        elif isinstance(result.value, bytes):
            data["value"] = result.value.hex()
        else:
            data["value"] = result.value
        
        return data
