"""
整数编码工具 - 将整数编码为字节数组
"""

import struct
from typing import Union


def encode_to_bytes(value: Union[int, int]) -> bytes:
    """
    将整数编码为字节数组，根据值的大小选择合适的字节长度
    
    Args:
        value: 要编码的整数值
        
    Returns:
        编码后的字节数组（大端序）
    """
    if value < 0:
        raise ValueError("Value must be non-negative")
    
    if 0 <= value <= 255:
        # 1 字节
        return struct.pack('>B', value)
    elif 0 <= value <= 65535:
        # 2 字节
        return struct.pack('>H', value)
    elif 0 <= value <= 4294967295:
        # 4 字节
        return struct.pack('>I', value)
    else:
        # 8 字节
        return struct.pack('>Q', value)


def decode_from_bytes(data: bytes) -> int:
    """
    从字节数组解码整数值
    
    Args:
        data: 要解码的字节数组
        
    Returns:
        解码后的整数值
    """
    if not data:
        return 0
    
    length = len(data)
    
    if length == 1:
        return struct.unpack('>B', data)[0]
    elif length == 2:
        return struct.unpack('>H', data)[0]
    elif length == 4:
        return struct.unpack('>I', data)[0]
    elif length == 8:
        return struct.unpack('>Q', data)[0]
    else:
        # 对于其他长度，使用通用方法
        result = 0
        for byte in data:
            result = (result << 8) | byte
        return result
