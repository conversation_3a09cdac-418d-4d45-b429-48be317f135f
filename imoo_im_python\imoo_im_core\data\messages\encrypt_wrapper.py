"""
加密包装器消息
"""

from ..base_message import BaseMessageWithoutRid
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(32)
class EncryptWrapper(BaseMessageWithoutRid):
    """加密包装器，用于包装加密后的消息数据"""

    def __init__(self):
        super().__init__()
        self._payload: bytes = b''

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'payload': 10
        }

    @property
    def payload(self) -> bytes:
        """加密后的消息载荷"""
        return self._payload

    @payload.setter
    def payload(self, value: bytes):
        self._payload = value
