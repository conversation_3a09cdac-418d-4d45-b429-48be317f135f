﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Message.Request.SyncFinishAckRequest
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;

#nullable disable
namespace iMooIm.Core.Message.Request;

[CommandId(15)]
internal class SyncFinishAckRequest : BaseMessage
{
  [TagId(11)]
  public long ImAccountId { get; set; }

  [TagId(12)]
  public long RegistId { get; set; }

  [TagId(13)]
  public long SyncKey { get; set; }
}
