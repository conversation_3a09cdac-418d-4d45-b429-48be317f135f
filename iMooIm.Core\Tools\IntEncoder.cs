﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Tools.IntEncoder
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using System;

#nullable enable
namespace iMooIm.Core.Tools;

internal static class IntEncoder
{
  internal static byte[] Encode2Bytes(this long value)
  {
    byte[] array;
    if (value >= 0L && value <= (long) byte.MaxValue)
      array = new byte[1]{ (byte) value };
    else
      array = value < 0L || value > (long) ushort.MaxValue ? (value < 0L || value > (long) uint.MaxValue ? BitConverter.GetBytes(value) : BitConverter.GetBytes((uint) value)) : BitConverter.GetBytes((ushort) value);
    if (BitConverter.IsLittleEndian)
      Array.Reverse<byte>(array);
    return array;
  }

  internal static byte[] Encode2Bytes(this int value)
  {
    byte[] array;
    if (value >= 0 && value <= (int) byte.MaxValue)
      array = new byte[1]{ (byte) value };
    else
      array = value < 0 || value > (int) ushort.MaxValue ? BitConverter.GetBytes(value) : BitConverter.GetBytes((ushort) value);
    if (BitConverter.IsLittleEndian)
      Array.Reverse<byte>(array);
    return array;
  }
}
