"""
加密工具模块 - 提供 RSA 公钥加密和 XOR 加密功能
"""

import base64
import re
from typing import Union
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend


class EncryptUtil:
    """加密工具类"""
    
    @staticmethod
    def convert_pem_to_der(pem_key: str) -> bytes:
        """
        将 PEM 格式的公钥转换为 DER 格式
        
        Args:
            pem_key: PEM 格式的公钥字符串
            
        Returns:
            DER 格式的公钥字节数组
            
        Raises:
            ValueError: 当 PEM 格式无效时
        """
        header = "-----BEGIN PUBLIC KEY-----"
        footer = "-----END PUBLIC KEY-----"
        
        start = pem_key.find(header)
        end = pem_key.find(footer)
        
        if start == -1 or end == -1:
            raise ValueError("Invalid PEM format")
        
        start += len(header)
        base64_key = pem_key[start:end].replace("\n", "").replace("\r", "").strip()
        
        try:
            return base64.b64decode(base64_key)
        except Exception as e:
            raise ValueError(f"Failed to decode base64: {e}")
    
    @staticmethod
    def encrypt_key(data_to_encrypt: bytes, public_key_pem: str) -> bytes:
        """
        使用 RSA 公钥加密数据
        
        Args:
            data_to_encrypt: 要加密的数据
            public_key_pem: PEM 格式的 RSA 公钥
            
        Returns:
            加密后的数据
            
        Raises:
            ValueError: 当公钥格式无效或加密失败时
        """
        try:
            # 加载公钥
            public_key = serialization.load_pem_public_key(
                public_key_pem.encode('utf-8'),
                backend=default_backend()
            )
            
            # 使用 PKCS1v15 填充进行加密
            encrypted = public_key.encrypt(
                data_to_encrypt,
                padding.PKCS1v15()
            )
            
            return encrypted
            
        except Exception as e:
            raise ValueError(f"Failed to encrypt with RSA: {e}")
    
    @staticmethod
    def encrypt(src_data: bytes, secret_key: bytes) -> bytes:
        """
        使用 XOR 算法加密数据
        
        Args:
            src_data: 源数据
            secret_key: 密钥
            
        Returns:
            加密后的数据
        """
        if not src_data or not secret_key:
            return src_data
        
        src_length = len(src_data)
        key_length = len(secret_key)
        
        if src_length == 0 or key_length == 0:
            return src_data
        
        result = bytearray()
        key_index = 0
        
        for i in range(src_length):
            if key_index >= key_length:
                key_index = 0
            
            encrypted_byte = src_data[i] ^ secret_key[key_index]
            result.append(encrypted_byte)
            key_index += 1
        
        return bytes(result)
    
    @staticmethod
    def decrypt(encrypted_data: bytes, secret_key: bytes) -> bytes:
        """
        使用 XOR 算法解密数据（XOR 加密和解密是相同的操作）
        
        Args:
            encrypted_data: 加密的数据
            secret_key: 密钥
            
        Returns:
            解密后的数据
        """
        return EncryptUtil.encrypt(encrypted_data, secret_key)
