"""
登录请求消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(3)
class LoginRequestMessage(BaseMessage):
    """
    登录请求消息类

    用于发送登录请求
    """

    def __init__(self):
        super().__init__()
        self._apns_token: str = ""
        self._apns_type: int = 0
        self._base_request_param: str = ""
        self._device_token: str = ""
        self._eebbk_key: str = ""
        self._im_sdk_version_name: str = ""
        self._platform: int = 0
        self._push_type: int = 0
        self._regist_id: int = 0
        self._regist_token: str = ""
        self._sdk_version: int = 0
        self._timestamp: int = 0

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'regist_id': 10,
            'regist_token': 11,
            'sdk_version': 12,
            'apns_type': 15,
            'apns_token': 16,
            'device_token': 17,
            'im_sdk_version_name': 18,
            'push_type': 19,
            'platform': 20,
            'timestamp': 21,
            'base_request_param': 24,
            'eebbk_key': 25
        }

    @property
    def apns_token(self) -> str:
        return self._apns_token

    @apns_token.setter
    def apns_token(self, value: str):
        self._apns_token = value

    @property
    def apns_type(self) -> int:
        return self._apns_type

    @apns_type.setter
    def apns_type(self, value: int):
        self._apns_type = value

    @property
    def base_request_param(self) -> str:
        return self._base_request_param

    @base_request_param.setter
    def base_request_param(self, value: str):
        self._base_request_param = value

    @property
    def device_token(self) -> str:
        return self._device_token

    @device_token.setter
    def device_token(self, value: str):
        self._device_token = value

    @property
    def eebbk_key(self) -> str:
        return self._eebbk_key

    @eebbk_key.setter
    def eebbk_key(self, value: str):
        self._eebbk_key = value

    @property
    def im_sdk_version_name(self) -> str:
        return self._im_sdk_version_name

    @im_sdk_version_name.setter
    def im_sdk_version_name(self, value: str):
        self._im_sdk_version_name = value

    @property
    def platform(self) -> int:
        return self._platform

    @platform.setter
    def platform(self, value: int):
        self._platform = value

    @property
    def push_type(self) -> int:
        return self._push_type

    @push_type.setter
    def push_type(self, value: int):
        self._push_type = value

    @property
    def regist_id(self) -> int:
        return self._regist_id

    @regist_id.setter
    def regist_id(self, value: int):
        self._regist_id = value

    @property
    def regist_token(self) -> str:
        return self._regist_token

    @regist_token.setter
    def regist_token(self, value: str):
        self._regist_token = value

    @property
    def sdk_version(self) -> int:
        return self._sdk_version

    @sdk_version.setter
    def sdk_version(self, value: int):
        self._sdk_version = value

    @property
    def timestamp(self) -> int:
        return self._timestamp

    @timestamp.setter
    def timestamp(self, value: int):
        self._timestamp = value
