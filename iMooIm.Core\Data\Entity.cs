﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Data.Entity
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using iMooIm.Core.Tlv;
using iMooIm.Core.Tools;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;

#nullable enable
namespace iMooIm.Core.Data;

internal class Entity
{
  internal Dictionary<int, object> Data { get; set; } = new Dictionary<int, object>();

  internal int CommandId { get; set; }

  internal byte[] Encode()
  {
    using (MemoryStream memoryStream1 = new MemoryStream())
    {
      using (MemoryStream memoryStream2 = new MemoryStream())
      {
        foreach (KeyValuePair<int, object> keyValuePair in this.Data)
        {
          byte[] numArray1 = Array.Empty<byte>();
          if (keyValuePair.Value is int)
            numArray1 = (keyValuePair.Value as int?).GetValueOrDefault().Encode2Bytes();
          if (keyValuePair.Value is long)
            numArray1 = (keyValuePair.Value as long?).GetValueOrDefault().Encode2Bytes();
          if (keyValuePair.Value is byte[] numArray2)
            numArray1 = numArray2;
          if (keyValuePair.Value is string s)
            numArray1 = Encoding.UTF8.GetBytes(s);
          memoryStream2.Write((ReadOnlySpan<byte>) TlvEncoder.EncodeTlv(keyValuePair.Key, numArray1.Length, numArray1));
        }
        memoryStream1.Write((ReadOnlySpan<byte>) TlvEncoder.EncodeNestedTlv(32 /*0x20*/, this.CommandId, memoryStream2.ToArray().Length, memoryStream2.ToArray()));
        return memoryStream1.ToArray();
      }
    }
  }
}
