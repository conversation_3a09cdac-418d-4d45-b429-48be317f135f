﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Tlv.TLVUtils
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using System;

#nullable enable
namespace iMooIm.Core.Tlv;

internal class TLVUtils
{
  internal static long m17981a(byte[] p0)
  {
    long num;
    switch (p0.Length)
    {
      case 1:
        num = (long) p0[0] & (long) byte.MaxValue;
        break;
      case 2:
        num = ((long) p0[0] & (long) byte.MaxValue) << 8 | (long) p0[1] & (long) byte.MaxValue;
        break;
      case 4:
        num = ((long) p0[0] & (long) byte.MaxValue) << 24 | ((long) p0[1] & (long) byte.MaxValue) << 16 /*0x10*/ | ((long) p0[2] & (long) byte.MaxValue) << 8 | (long) p0[3] & (long) byte.MaxValue;
        break;
      case 8:
        num = ((long) p0[0] & (long) byte.MaxValue) << 56 | ((long) p0[1] & (long) byte.MaxValue) << 48 /*0x30*/ | ((long) p0[2] & (long) byte.MaxValue) << 40 | ((long) p0[3] & (long) byte.MaxValue) << 32 /*0x20*/ | ((long) p0[4] & (long) byte.MaxValue) << 24 | ((long) p0[5] & (long) byte.MaxValue) << 16 /*0x10*/ | ((long) p0[6] & (long) byte.MaxValue) << 8 | (long) p0[7] & (long) byte.MaxValue;
        break;
      default:
        throw new ArgumentException("the length of byte array is incorrect.");
    }
    return num;
  }

  internal static byte[] m17982a(long j)
  {
    if (j == (long) (byte) j)
      return TLVUtils.m17983a(j, 1);
    if (j == (long) (short) j)
      return TLVUtils.m17983a(j, 2);
    return j == (long) (int) j ? TLVUtils.m17983a(j, 4) : TLVUtils.m17983a(j, 8);
  }

  private static byte[] m17983a(long j, int i)
  {
    byte[] numArray = new byte[i];
    for (int index = 0; index < i; ++index)
      numArray[index] = (byte) (j >>> (i - index - 1) * 8);
    return numArray;
  }
}
