﻿// Decompiled with JetBrains decompiler
// Type: iMooIm.Core.Data.BaseRequestParam
// Assembly: iMooIm.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// MVID: B8185B2E-E607-4896-8285-C8B420735ED6
// Assembly location: D:\桌面\iMooIm\iMooIm.Core\iMooIm.Core.dll

using System;

#nullable enable
namespace iMooIm.Core.Data;

internal class BaseRequestParam
{
  internal string imFlag { get; set; } = "1";

  internal string appId { get; set; } = "1";

  internal string program { get; set; } = "watch";

  internal string timestamp { get; set; } = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

  internal long registId { get; set; }

  internal string deviceId { get; set; } = "";

  internal string token { get; set; } = "";

  internal string accountId { get; set; } = "";
}
