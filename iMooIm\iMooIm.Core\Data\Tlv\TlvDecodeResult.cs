﻿using System.Text;

namespace iMooIm.Core.Data.Tlv;

public class TLVDecodeResult
{

    public int FrameType { get; set; }

    public Object Value{ get; set; }
    public int DataType{ get; set; }
    public int TagValue{ get; set; }
    public int Length{ get; set; }

    public int toIntValue()
    {
        Object obj = this.Value;
        if (obj is byte[]) {
            return (int)TLVUtils.m17981a((byte[])obj);
        }
        return 0;
    }

    public long toLongValue()
    {
        Object obj = this.Value;
        if (obj is byte[]) {
            return TLVUtils.m17981a((byte[])obj);
        }
        return 0L;
    }

    public String toStringValue()
    {
        Object obj = this.Value;
        if (obj is byte[])
        {
            return Encoding.UTF8.GetString((byte[])obj);
        }
        return "";
    }
}
