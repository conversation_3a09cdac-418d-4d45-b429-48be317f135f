"""
登录响应消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(4)
class LoginResponseMessage(BaseMessage):
    """
    登录响应消息类

    服务器返回的登录响应
    """

    def __init__(self):
        super().__init__()
        self._code: int = 0
        self._desc: str = ""
        self._encrypt_key: str = ""
        self._encrypt_type: int = 0
        self._server_time: int = 0

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'code': 2,
            'desc': 3,
            'encrypt_key': 10,
            'encrypt_type': 11,
            'server_time': 12
        }

    @property
    def code(self) -> int:
        return self._code

    @code.setter
    def code(self, value: int):
        self._code = value

    @property
    def desc(self) -> str:
        return self._desc

    @desc.setter
    def desc(self, value: str):
        self._desc = value

    @property
    def encrypt_key(self) -> str:
        return self._encrypt_key

    @encrypt_key.setter
    def encrypt_key(self, value: str):
        self._encrypt_key = value

    @property
    def encrypt_type(self) -> int:
        return self._encrypt_type

    @encrypt_type.setter
    def encrypt_type(self, value: int):
        self._encrypt_type = value

    @property
    def server_time(self) -> int:
        return self._server_time

    @server_time.setter
    def server_time(self, value: int):
        self._server_time = value
