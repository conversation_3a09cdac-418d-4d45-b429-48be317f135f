"""
账户响应消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(6)
class AccountResponseMessage(BaseMessage):
    """
    账户响应消息类

    服务器返回的账户设置响应
    """

    def __init__(self):
        super().__init__()
        self._code: int = 0
        self._desc: str = ""
        self._im_account_id: int = 0
        self._account_token: str = ""

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'code': 2,
            'desc': 3,
            'im_account_id': 10,
            'account_token': 11
        }

    @property
    def code(self) -> int:
        """响应代码"""
        return self._code

    @code.setter
    def code(self, value: int):
        self._code = value

    @property
    def desc(self) -> str:
        """响应描述"""
        return self._desc

    @desc.setter
    def desc(self, value: str):
        self._desc = value

    @property
    def im_account_id(self) -> int:
        """IM账户ID"""
        return self._im_account_id

    @im_account_id.setter
    def im_account_id(self, value: int):
        self._im_account_id = value

    @property
    def account_token(self) -> str:
        """账户令牌"""
        return self._account_token

    @account_token.setter
    def account_token(self, value: str):
        self._account_token = value
