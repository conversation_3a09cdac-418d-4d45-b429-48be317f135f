"""
公钥响应消息
"""

from ..base_message import BaseMessage
from ..attributes.command_id import command_id
from ..attributes.tag_id import tag_id


@command_id(23)
class PublicKeyResponseMessage(BaseMessage):
    """公钥响应消息，包含服务器返回的公钥信息"""

    def __init__(self):
        super().__init__()
        self._code: int = 0
        self._desc: str = ""
        self._exponent: bytes = b''
        self._modulus: bytes = b''
        self._public_key: str = ""

        # 设置标签 ID 映射（覆盖基类的映射）
        self.__class__._tag_ids = {
            'r_id': 1,
            'code': 2,
            'desc': 3,
            'public_key': 10,
            'modulus': 11,
            'exponent': 12
        }

    @property
    def code(self) -> int:
        """响应代码"""
        return self._code

    @code.setter
    def code(self, value: int):
        self._code = value

    @property
    def desc(self) -> str:
        """响应描述"""
        return self._desc

    @desc.setter
    def desc(self, value: str):
        self._desc = value

    @property
    def exponent(self) -> bytes:
        """RSA 公钥指数"""
        return self._exponent

    @exponent.setter
    def exponent(self, value: bytes):
        self._exponent = value

    @property
    def modulus(self) -> bytes:
        """RSA 公钥模数"""
        return self._modulus

    @modulus.setter
    def modulus(self, value: bytes):
        self._modulus = value

    @property
    def public_key(self) -> str:
        """PEM 格式的公钥"""
        return self._public_key

    @public_key.setter
    def public_key(self, value: str):
        self._public_key = value
