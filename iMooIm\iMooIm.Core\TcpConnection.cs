﻿using System.Net.Sockets;
using System.Reflection;
using System.Text.Json;
using iMooIm.Core.Data;
using iMooIm.Core.Data.Attributes;
using iMooIm.Core.Data.Enums;
using iMooIm.Core.Data.Messages;
using iMooIm.Core.Data.Tlv;
using iMooIm.Core.Events;
using iMooIm.Core.Tools;

namespace iMooIm.Core;

public class TcpMessage
{
    public ManualResetEvent Lock;
    public object? Response;
}
public class TcpConnection
{
    private readonly Socket? _socket;
    private byte[] buffer = new byte[1024];
    private Dictionary<int,TcpMessage> _messageMap = new();
    public static readonly byte[] EncryptKey = Guid.NewGuid().ToByteArray();
    public event EventHandler<LogEvent>? OnLog;
    public event EventHandler<ReceiveMessageEvent>? OnReceiveMessage;
    public TcpConnection()
    {
        _socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
    }
    public T GetResponse<T>(int rId) where T : BaseMessage
    {
        check:
        if (_messageMap.ContainsKey(rId))
        {
            _messageMap[rId].Lock.WaitOne();
            return (T)_messageMap[rId].Response!;
        }
        OnLog?.Invoke(this,new LogEvent()
        {
            Level = LogLevel.Warning,
            Message = "Response not found id:"+rId+" waiting..."
        });
        Thread.Sleep(50);
        goto check;
        
    }
    public void Init(string host,int port)
    {
        _socket?.Connect(host,port);
        if (_socket?.Connected ?? false)
        {
            OnLog?.Invoke(this,new()
            {
                Message="Connected to " + host + ":" + port,
                Level= LogLevel.Information
            });
        }
        else
        {
            OnLog?.Invoke(this,new()
            {
                Level = LogLevel.Critical,
                Message = "Failed to connect to " + host + ":" + port
            });
            throw new Exception("Failed to connect to " + host + ":" + port);
        }
        _socket.BeginReceive(buffer, 0, buffer.Length, SocketFlags.None, new AsyncCallback(ReceiveCallback), _socket);
    }
    private void ReceiveCallback(IAsyncResult result)
    {
        Socket? ts = (Socket)result.AsyncState!;
        if (MessageDecoder.Decode(buffer) is BaseMessage req)
        {
            if (_messageMap.ContainsKey(req.RId))
            {
                
                OnLog?.Invoke(this,new LogEvent()
                {
                    Level = LogLevel.Trace,
                    Message = "Received message: " + JsonSerializer.Serialize(MessageDecoder.Decode(buffer))
                });
                _messageMap[req.RId].Response = MessageDecoder.Decode(buffer);
                _messageMap[req.RId].Lock.Set();
            }
            else
            {
                OnLog?.Invoke(this,new LogEvent()
                {
                    Level = LogLevel.Warning,
                    Message = "Received message: "+req.RId+" but not found in message map"
                });
            }
        }
        ts.EndReceive(result);
        result.AsyncWaitHandle.Close();
        buffer = new byte[buffer.Length];
        ts.BeginReceive(buffer, 0, buffer.Length, SocketFlags.None, new (ReceiveCallback), ts);
    }
    public void Send(byte[] data)
    {
        if (MessageDecoder.Decode(data) is BaseMessage req)
        {
            var attrs = req.GetType().GetCustomAttributes();
            int command = 0;
            foreach (var attr in attrs)
            {
                if (attr is CommandId commandId)
                {
                    command = commandId.Id;
                }
            }
            _messageMap.Add(req.RId,new TcpMessage
            {
                Lock=new ManualResetEvent(false),
                Response = null
            });
            bool isEncrypt = !(command is 7 or 22 or 33);
            OnLog?.Invoke(this,new LogEvent()
            {
                Level = LogLevel.Debug,
                Message = "Sending message: " + req.RId + " command: " + command + " isEncrypt: "+isEncrypt
            });
            var sendData = data;
            if (isEncrypt)
            {
                var encrypt = new EncryptWrapper();
                encrypt.Payload = EncryptUtil.Encrypt(sendData, EncryptKey);
                sendData = encrypt.Encode();
            }
            _socket?.Send(sendData);
        }
        else
        {
            throw new ArgumentException("Invalid message");
        }
    }
}